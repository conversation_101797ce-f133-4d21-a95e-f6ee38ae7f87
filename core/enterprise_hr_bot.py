"""
Enterprise-grade HR Bot with enhanced architecture, performance, and security.
This is the main application entry point with all enterprise features integrated.
"""

import asyncio
import logging
import os
import sys
import time
from contextlib import asynccontextmanager
from typing import Dict, Any, Optional

import uvicorn
from fastapi import FastAPI, Request, HTTPException, Header, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse

# Import performance components
from performance.cache_manager import cache_manager
from performance.connection_pool import async_pool, sync_pool, initialize_pools, close_pools
from performance.async_optimizer import async_request_manager, async_tool_executor, async_llm_manager

# Import security components
from security.rbac_manager import rbac_manager, require_permission, Permission
from security.input_validator import hr_validator, validate_input
from security.rate_limiter import rate_limiter, rate_limit
from security.audit_logger import audit_logger, audit_log, AuditEventType, AuditSeverity

# Import existing components
from packages import *
from validate_emp import validate_employee
from hr_bot import (
    <PERSON>wenChatLLM, AgentState, call_model, tool_router, should_continue,
    TOOL_FUNCTIONS, generate_chat_heading, check_session_exists,
    create_new_session, log_chat_interaction
)
from tool_schema import TOOLS_SCHEMA

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('/var/log/hr_bot/application.log')
    ]
)
logger = logging.getLogger(__name__)

class EnterpriseHRBot:
    """
    Enterprise-grade HR Bot with enhanced architecture
    """
    
    def __init__(self):
        self.app = None
        self.config = self._load_config()
        self.is_initialized = False
    
    def _load_config(self) -> Dict[str, Any]:
        """Load application configuration"""
        return {
            "app": {
                "title": "Enterprise HR Bot",
                "version": "2.0.0",
                "description": "Production-ready HR assistance system",
                "debug": os.getenv("DEBUG", "false").lower() == "true"
            },
            "server": {
                "host": os.getenv("HOST", "0.0.0.0"),
                "port": int(os.getenv("API_PORT", "5011")),
                "workers": int(os.getenv("WORKERS", "4")),
                "max_requests": int(os.getenv("MAX_REQUESTS", "1000")),
                "max_requests_jitter": int(os.getenv("MAX_REQUESTS_JITTER", "100"))
            },
            "security": {
                "allowed_hosts": os.getenv("ALLOWED_HOSTS", "*").split(","),
                "cors_origins": os.getenv("CORS_ORIGINS", "*").split(","),
                "jwt_secret": os.getenv("JWT_SECRET_KEY"),
                "encryption_key": os.getenv("ENCRYPTION_KEY")
            },
            "performance": {
                "enable_caching": os.getenv("ENABLE_CACHING", "true").lower() == "true",
                "enable_async": os.getenv("ENABLE_ASYNC", "true").lower() == "true",
                "max_concurrent_requests": int(os.getenv("MAX_CONCURRENT_REQUESTS", "100"))
            }
        }
    
    @asynccontextmanager
    async def lifespan(self, app: FastAPI):
        """Application lifespan management"""
        # Startup
        logger.info("Starting Enterprise HR Bot...")
        
        try:
            # Initialize connection pools
            await initialize_pools()
            logger.info("Connection pools initialized")
            
            # Initialize cache
            if self.config["performance"]["enable_caching"]:
                cache_health = cache_manager.health_check()
                if cache_health["status"] == "healthy":
                    logger.info("Cache system initialized")
                else:
                    logger.warning(f"Cache system unhealthy: {cache_health}")
            
            # Initialize async components
            if self.config["performance"]["enable_async"]:
                await async_request_manager.initialize()
                logger.info("Async request manager initialized")
            
            # Test database connectivity
            pool_health = await async_pool.health_check()
            logger.info(f"Database health: {pool_health}")
            
            self.is_initialized = True
            logger.info("Enterprise HR Bot startup complete")
            
            yield
            
        except Exception as e:
            logger.error(f"Startup failed: {e}")
            raise
        
        finally:
            # Shutdown
            logger.info("Shutting down Enterprise HR Bot...")
            
            try:
                await close_pools()
                await async_request_manager.close()
                logger.info("Cleanup complete")
            except Exception as e:
                logger.error(f"Shutdown error: {e}")
    
    def create_app(self) -> FastAPI:
        """Create and configure FastAPI application"""
        
        app = FastAPI(
            title=self.config["app"]["title"],
            version=self.config["app"]["version"],
            description=self.config["app"]["description"],
            debug=self.config["app"]["debug"],
            lifespan=self.lifespan
        )
        
        # Add security middleware
        if self.config["security"]["allowed_hosts"] != ["*"]:
            app.add_middleware(
                TrustedHostMiddleware,
                allowed_hosts=self.config["security"]["allowed_hosts"]
            )
        
        # Add CORS middleware
        app.add_middleware(
            CORSMiddleware,
            allow_origins=self.config["security"]["cors_origins"],
            allow_credentials=True,
            allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            allow_headers=[
                "Authorization", "Content-Type", "Accept", "Origin",
                "User-Agent", "DNT", "Cache-Control", "X-Mx-ReqToken",
                "Keep-Alive", "X-Requested-With", "If-Modified-Since", "X-CSRF-Token"
            ],
            expose_headers=["*"]
        )
        
        # Add custom middleware
        app.middleware("http")(self.security_middleware)
        app.middleware("http")(self.performance_middleware)
        app.middleware("http")(self.audit_middleware)
        
        # Register routes
        self._register_routes(app)
        
        # Register exception handlers
        self._register_exception_handlers(app)
        
        self.app = app
        return app
    
    async def security_middleware(self, request: Request, call_next):
        """Security middleware for request processing"""
        start_time = time.time()
        
        try:
            # Extract client information
            client_ip = self._get_client_ip(request)
            user_agent = request.headers.get("user-agent", "")
            
            # Basic security checks
            if len(request.url.path) > 1000:
                raise HTTPException(status_code=414, detail="URI too long")
            
            if request.headers.get("content-length"):
                content_length = int(request.headers.get("content-length", 0))
                if content_length > 10 * 1024 * 1024:  # 10MB limit
                    raise HTTPException(status_code=413, detail="Request entity too large")
            
            # Process request
            response = await call_next(request)
            
            # Add security headers
            response.headers["X-Content-Type-Options"] = "nosniff"
            response.headers["X-Frame-Options"] = "DENY"
            response.headers["X-XSS-Protection"] = "1; mode=block"
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
            response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
            
            return response
            
        except Exception as e:
            logger.error(f"Security middleware error: {e}")
            raise
    
    async def performance_middleware(self, request: Request, call_next):
        """Performance monitoring middleware"""
        start_time = time.time()
        
        try:
            response = await call_next(request)
            
            # Add performance headers
            process_time = time.time() - start_time
            response.headers["X-Process-Time"] = str(process_time)
            
            # Log slow requests
            if process_time > 5.0:  # 5 seconds threshold
                logger.warning(f"Slow request: {request.url.path} took {process_time:.2f}s")
            
            return response
            
        except Exception as e:
            process_time = time.time() - start_time
            logger.error(f"Request failed after {process_time:.2f}s: {e}")
            raise
    
    async def audit_middleware(self, request: Request, call_next):
        """Audit logging middleware"""
        start_time = time.time()
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get("user-agent", "")
        
        try:
            response = await call_next(request)
            
            # Log successful requests
            if request.url.path.startswith("/ask_hr"):
                audit_logger.log_event(
                    event_type=AuditEventType.DATA_VIEW,
                    severity=AuditSeverity.LOW,
                    resource=request.url.path,
                    action="request",
                    result="success",
                    ip_address=client_ip,
                    user_agent=user_agent,
                    details={
                        "method": request.method,
                        "status_code": response.status_code,
                        "response_time": time.time() - start_time
                    }
                )
            
            return response
            
        except Exception as e:
            # Log failed requests
            audit_logger.log_event(
                event_type=AuditEventType.SYSTEM_ERROR,
                severity=AuditSeverity.HIGH,
                resource=request.url.path,
                action="request",
                result="error",
                ip_address=client_ip,
                user_agent=user_agent,
                details={
                    "method": request.method,
                    "error": str(e),
                    "response_time": time.time() - start_time
                }
            )
            raise
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address"""
        x_forwarded_for = request.headers.get('x-forwarded-for')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0].strip()
        return request.client.host if request.client else "unknown"
    
    def _register_routes(self, app: FastAPI):
        """Register API routes"""
        
        @app.get("/health")
        async def health_check():
            """Comprehensive health check"""
            health_status = {
                "status": "healthy" if self.is_initialized else "initializing",
                "timestamp": datetime.now().isoformat(),
                "version": self.config["app"]["version"],
                "components": {}
            }
            
            # Check database
            try:
                db_health = await async_pool.health_check()
                health_status["components"]["database"] = db_health
            except Exception as e:
                health_status["components"]["database"] = {"status": "unhealthy", "error": str(e)}
            
            # Check cache
            if self.config["performance"]["enable_caching"]:
                cache_health = cache_manager.health_check()
                health_status["components"]["cache"] = cache_health
            
            # Check rate limiter
            try:
                # Simple rate limiter test
                allowed, _ = rate_limiter.is_request_allowed("health", None, "127.0.0.1")
                health_status["components"]["rate_limiter"] = {
                    "status": "healthy" if allowed else "limited"
                }
            except Exception as e:
                health_status["components"]["rate_limiter"] = {"status": "unhealthy", "error": str(e)}
            
            # Determine overall status
            component_statuses = [comp.get("status") for comp in health_status["components"].values()]
            if any(status == "unhealthy" for status in component_statuses):
                health_status["status"] = "unhealthy"
            elif any(status == "limited" for status in component_statuses):
                health_status["status"] = "degraded"
            
            status_code = 200 if health_status["status"] == "healthy" else 503
            return JSONResponse(content=health_status, status_code=status_code)
        
        # Import and register HR routes
        from routes.enterprise_apis import register_hr_routes
        register_hr_routes(app, self)
    
    def _register_exception_handlers(self, app: FastAPI):
        """Register global exception handlers"""
        
        @app.exception_handler(HTTPException)
        async def http_exception_handler(request: Request, exc: HTTPException):
            """Handle HTTP exceptions"""
            return JSONResponse(
                status_code=exc.status_code,
                content={
                    "error": exc.status_code,
                    "message": exc.detail,
                    "timestamp": datetime.now().isoformat()
                }
            )
        
        @app.exception_handler(Exception)
        async def general_exception_handler(request: Request, exc: Exception):
            """Handle general exceptions"""
            logger.error(f"Unhandled exception: {exc}", exc_info=True)
            
            return JSONResponse(
                status_code=500,
                content={
                    "error": 500,
                    "message": "Internal server error",
                    "timestamp": datetime.now().isoformat()
                }
            )
    
    def run(self):
        """Run the application"""
        app = self.create_app()
        
        uvicorn.run(
            app,
            host=self.config["server"]["host"],
            port=self.config["server"]["port"],
            proxy_headers=True,
            forwarded_allow_ips='*',
            access_log=True,
            log_level="info"
        )

# Global application instance
enterprise_hr_bot = EnterpriseHRBot()

if __name__ == "__main__":
    enterprise_hr_bot.run()
