"""
Async optimization layer for HR Bot to handle concurrent requests efficiently.
Implements async/await patterns, request batching, and parallel processing.
"""

import asyncio
import aiohttp
from typing import List, Dict, Any, Optional, Callable, Awaitable
import time
import logging
from dataclasses import dataclass
from concurrent.futures import Thr<PERSON><PERSON>ool<PERSON>xecutor, as_completed
import functools
from contextlib import asynccontextmanager
import json

logger = logging.getLogger(__name__)

@dataclass
class AsyncConfig:
    """Configuration for async operations"""
    max_concurrent_requests: int = 50
    request_timeout: int = 30
    batch_size: int = 10
    retry_attempts: int = 3
    retry_delay: float = 1.0
    circuit_breaker_threshold: int = 5
    circuit_breaker_timeout: int = 60

class CircuitBreaker:
    """Circuit breaker pattern for external API calls"""
    
    def __init__(self, failure_threshold: int = 5, timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, O<PERSON><PERSON>, H<PERSON><PERSON>_<PERSON><PERSON>EN
    
    def can_execute(self) -> bool:
        """Check if request can be executed"""
        if self.state == 'CLOSED':
            return True
        elif self.state == 'OPEN':
            if time.time() - self.last_failure_time > self.timeout:
                self.state = 'HALF_OPEN'
                return True
            return False
        else:  # HALF_OPEN
            return True
    
    def record_success(self):
        """Record successful execution"""
        self.failure_count = 0
        self.state = 'CLOSED'
    
    def record_failure(self):
        """Record failed execution"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = 'OPEN'

class AsyncRequestManager:
    """
    Manages async HTTP requests with connection pooling, retries, and circuit breakers
    """
    
    def __init__(self, config: AsyncConfig = None):
        self.config = config or AsyncConfig()
        self.session = None
        self.circuit_breakers = {}
        self.semaphore = asyncio.Semaphore(self.config.max_concurrent_requests)
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def initialize(self):
        """Initialize HTTP session"""
        if not self.session:
            connector = aiohttp.TCPConnector(
                limit=100,
                limit_per_host=30,
                ttl_dns_cache=300,
                use_dns_cache=True
            )
            
            timeout = aiohttp.ClientTimeout(total=self.config.request_timeout)
            
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={'User-Agent': 'HR-Bot-Async/1.0'}
            )
    
    async def close(self):
        """Close HTTP session"""
        if self.session:
            await self.session.close()
            self.session = None
    
    def get_circuit_breaker(self, service_name: str) -> CircuitBreaker:
        """Get or create circuit breaker for service"""
        if service_name not in self.circuit_breakers:
            self.circuit_breakers[service_name] = CircuitBreaker(
                self.config.circuit_breaker_threshold,
                self.config.circuit_breaker_timeout
            )
        return self.circuit_breakers[service_name]
    
    async def make_request(
        self,
        method: str,
        url: str,
        service_name: str,
        **kwargs
    ) -> Optional[Dict[str, Any]]:
        """
        Make async HTTP request with circuit breaker and retry logic
        """
        circuit_breaker = self.get_circuit_breaker(service_name)
        
        if not circuit_breaker.can_execute():
            logger.warning(f"Circuit breaker OPEN for {service_name}")
            return None
        
        async with self.semaphore:
            for attempt in range(self.config.retry_attempts):
                try:
                    if not self.session:
                        await self.initialize()
                    
                    async with self.session.request(method, url, **kwargs) as response:
                        if response.status == 200:
                            circuit_breaker.record_success()
                            return await response.json()
                        else:
                            logger.warning(f"HTTP {response.status} for {url}")
                            
                except asyncio.TimeoutError:
                    logger.warning(f"Timeout for {url} (attempt {attempt + 1})")
                except Exception as e:
                    logger.error(f"Request error for {url}: {e} (attempt {attempt + 1})")
                
                if attempt < self.config.retry_attempts - 1:
                    await asyncio.sleep(self.config.retry_delay * (2 ** attempt))
            
            circuit_breaker.record_failure()
            return None

class AsyncBatchProcessor:
    """
    Processes multiple requests in batches for optimal performance
    """
    
    def __init__(self, batch_size: int = 10, max_workers: int = 5):
        self.batch_size = batch_size
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
    
    async def process_batch(
        self,
        items: List[Any],
        processor_func: Callable[[Any], Awaitable[Any]]
    ) -> List[Any]:
        """Process items in batches"""
        results = []
        
        for i in range(0, len(items), self.batch_size):
            batch = items[i:i + self.batch_size]
            batch_tasks = [processor_func(item) for item in batch]
            
            try:
                batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
                results.extend(batch_results)
            except Exception as e:
                logger.error(f"Batch processing error: {e}")
                results.extend([None] * len(batch))
        
        return results
    
    async def process_parallel(
        self,
        items: List[Any],
        processor_func: Callable[[Any], Any],
        use_threads: bool = True
    ) -> List[Any]:
        """Process items in parallel using threads or async"""
        if use_threads:
            loop = asyncio.get_event_loop()
            tasks = [
                loop.run_in_executor(self.executor, processor_func, item)
                for item in items
            ]
        else:
            tasks = [processor_func(item) for item in items]
        
        try:
            return await asyncio.gather(*tasks, return_exceptions=True)
        except Exception as e:
            logger.error(f"Parallel processing error: {e}")
            return [None] * len(items)

class AsyncToolExecutor:
    """
    Executes HR tools asynchronously with intelligent batching and caching
    """
    
    def __init__(self, request_manager: AsyncRequestManager = None):
        self.request_manager = request_manager or AsyncRequestManager()
        self.batch_processor = AsyncBatchProcessor()
        self._tool_cache = {}
    
    async def execute_tool_async(
        self,
        tool_name: str,
        tool_func: Callable,
        *args,
        **kwargs
    ) -> Any:
        """Execute a single tool asynchronously"""
        try:
            # Check if tool function is already async
            if asyncio.iscoroutinefunction(tool_func):
                return await tool_func(*args, **kwargs)
            else:
                # Run in thread pool for CPU-bound operations
                loop = asyncio.get_event_loop()
                return await loop.run_in_executor(
                    self.batch_processor.executor,
                    tool_func,
                    *args,
                    **kwargs
                )
        except Exception as e:
            logger.error(f"Tool execution error for {tool_name}: {e}")
            return f"Error executing {tool_name}: {str(e)}"
    
    async def execute_multiple_tools(
        self,
        tool_calls: List[Dict[str, Any]],
        tool_functions: Dict[str, Callable]
    ) -> List[Any]:
        """Execute multiple tools concurrently"""
        async def execute_single_tool(tool_call):
            tool_name = tool_call.get('function', {}).get('name')
            tool_args = tool_call.get('function', {}).get('arguments', {})
            
            if isinstance(tool_args, str):
                try:
                    tool_args = json.loads(tool_args)
                except json.JSONDecodeError:
                    tool_args = {}
            
            tool_func = tool_functions.get(tool_name)
            if not tool_func:
                return f"Tool {tool_name} not found"
            
            return await self.execute_tool_async(tool_name, tool_func, **tool_args)
        
        # Execute all tools concurrently
        tasks = [execute_single_tool(tool_call) for tool_call in tool_calls]
        return await asyncio.gather(*tasks, return_exceptions=True)

class AsyncLLMManager:
    """
    Manages LLM requests with async optimization and intelligent batching
    """
    
    def __init__(self, request_manager: AsyncRequestManager = None):
        self.request_manager = request_manager or AsyncRequestManager()
        self._response_cache = {}
    
    async def call_llm_async(
        self,
        messages: List[Dict],
        model: str,
        base_url: str,
        tools: Optional[List] = None,
        **kwargs
    ) -> Optional[Dict]:
        """Make async LLM API call"""
        payload = {
            "model": model,
            "messages": messages,
            "temperature": kwargs.get("temperature", 0),
        }
        
        if tools:
            payload["tools"] = tools
            payload["tool_choice"] = "auto"
        
        try:
            result = await self.request_manager.make_request(
                "POST",
                base_url,
                "llm_service",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            return result
        except Exception as e:
            logger.error(f"LLM API call failed: {e}")
            return None
    
    async def batch_llm_calls(
        self,
        requests: List[Dict],
        model: str,
        base_url: str
    ) -> List[Optional[Dict]]:
        """Process multiple LLM requests in batch"""
        async def process_single_request(request_data):
            return await self.call_llm_async(
                request_data["messages"],
                model,
                base_url,
                request_data.get("tools"),
                **request_data.get("kwargs", {})
            )
        
        return await self.batch_processor.process_batch(
            requests,
            process_single_request
        )

# Utility functions for async optimization

async def run_with_timeout(coro, timeout: float):
    """Run coroutine with timeout"""
    try:
        return await asyncio.wait_for(coro, timeout=timeout)
    except asyncio.TimeoutError:
        logger.warning(f"Operation timed out after {timeout} seconds")
        return None

async def gather_with_concurrency(tasks: List, max_concurrency: int = 10):
    """Run tasks with limited concurrency"""
    semaphore = asyncio.Semaphore(max_concurrency)
    
    async def run_with_semaphore(task):
        async with semaphore:
            return await task
    
    return await asyncio.gather(
        *[run_with_semaphore(task) for task in tasks],
        return_exceptions=True
    )

def async_retry(max_attempts: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """Decorator for async retry logic"""
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_attempts):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_attempts - 1:
                        wait_time = delay * (backoff ** attempt)
                        logger.warning(f"Attempt {attempt + 1} failed, retrying in {wait_time}s: {e}")
                        await asyncio.sleep(wait_time)
                    else:
                        logger.error(f"All {max_attempts} attempts failed: {e}")
            
            raise last_exception
        return wrapper
    return decorator

# Global instances
async_request_manager = AsyncRequestManager()
async_tool_executor = AsyncToolExecutor(async_request_manager)
async_llm_manager = AsyncLLMManager(async_request_manager)
