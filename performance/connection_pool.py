"""
Enterprise-grade connection pooling for database and external API connections.
Implements async connection pools with health checks and automatic failover.
"""

import asyncio
import aiohttp
import motor.motor_asyncio
from pymongo import MongoClient
from typing import Dict, Any, Optional, List
import logging
import time
from dataclasses import dataclass
from contextlib import asynccontextmanager
import os
import urllib.parse
from concurrent.futures import ThreadPoolExecutor
import threading

logger = logging.getLogger(__name__)

@dataclass
class ConnectionConfig:
    """Connection pool configuration"""
    # MongoDB settings
    mongo_host: str = os.getenv("MONGO_HOST", "**************")
    mongo_port: int = int(os.getenv("MONGO_PORT", "27017"))
    mongo_user: str = os.getenv("MONGO_USER", "jdsocial")
    mongo_password: str = os.getenv("MONGO_PASSWORD", "jdsocial123")
    mongo_db: str = os.getenv("MONGO_DB", "jdsocial")
    mongo_max_pool_size: int = int(os.getenv("MONGO_MAX_POOL_SIZE", "50"))
    mongo_min_pool_size: int = int(os.getenv("MONGO_MIN_POOL_SIZE", "10"))
    
    # HTTP client settings
    http_max_connections: int = int(os.getenv("HTTP_MAX_CONNECTIONS", "100"))
    http_max_connections_per_host: int = int(os.getenv("HTTP_MAX_CONNECTIONS_PER_HOST", "30"))
    http_timeout: int = int(os.getenv("HTTP_TIMEOUT", "30"))
    
    # Thread pool settings
    thread_pool_max_workers: int = int(os.getenv("THREAD_POOL_MAX_WORKERS", "20"))

class AsyncConnectionPool:
    """
    Async connection pool manager for MongoDB and HTTP clients
    """
    
    def __init__(self, config: ConnectionConfig = None):
        self.config = config or ConnectionConfig()
        self._mongo_client = None
        self._http_session = None
        self._thread_pool = None
        self._lock = asyncio.Lock()
        
    async def initialize(self):
        """Initialize all connection pools"""
        async with self._lock:
            await self._init_mongo_pool()
            await self._init_http_pool()
            self._init_thread_pool()
            logger.info("All connection pools initialized successfully")
    
    async def _init_mongo_pool(self):
        """Initialize MongoDB async connection pool"""
        try:
            mongo_uri = self._build_mongo_uri()
            self._mongo_client = motor.motor_asyncio.AsyncIOMotorClient(
                mongo_uri,
                maxPoolSize=self.config.mongo_max_pool_size,
                minPoolSize=self.config.mongo_min_pool_size,
                maxIdleTimeMS=30000,
                waitQueueTimeoutMS=5000,
                serverSelectionTimeoutMS=5000,
                connectTimeoutMS=10000,
                socketTimeoutMS=20000,
                retryWrites=True,
                retryReads=True
            )
            
            # Test connection
            await self._mongo_client.admin.command('ping')
            logger.info("MongoDB async connection pool initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize MongoDB pool: {e}")
            self._mongo_client = None
    
    async def _init_http_pool(self):
        """Initialize HTTP client session pool"""
        try:
            connector = aiohttp.TCPConnector(
                limit=self.config.http_max_connections,
                limit_per_host=self.config.http_max_connections_per_host,
                ttl_dns_cache=300,
                use_dns_cache=True,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )
            
            timeout = aiohttp.ClientTimeout(total=self.config.http_timeout)
            
            self._http_session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={'User-Agent': 'HR-Bot/1.0'}
            )
            
            logger.info("HTTP connection pool initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize HTTP pool: {e}")
            self._http_session = None
    
    def _init_thread_pool(self):
        """Initialize thread pool for CPU-bound tasks"""
        try:
            self._thread_pool = ThreadPoolExecutor(
                max_workers=self.config.thread_pool_max_workers,
                thread_name_prefix="hr_bot_worker"
            )
            logger.info("Thread pool initialized")
        except Exception as e:
            logger.error(f"Failed to initialize thread pool: {e}")
            self._thread_pool = None
    
    def _build_mongo_uri(self) -> str:
        """Build MongoDB connection URI"""
        if "social" in self.config.mongo_user:
            return f"mongodb://{self.config.mongo_user}:{self.config.mongo_password}@{self.config.mongo_host}:{self.config.mongo_port}/{self.config.mongo_db}?compressors=zlib&directConnection=true"
        
        password_encoded = urllib.parse.quote_plus(self.config.mongo_password)
        return f"mongodb://{self.config.mongo_user}:{password_encoded}@{self.config.mongo_host}:{self.config.mongo_port}/{self.config.mongo_db}"
    
    @asynccontextmanager
    async def get_mongo_db(self):
        """Get MongoDB database connection"""
        if not self._mongo_client:
            await self._init_mongo_pool()
        
        if self._mongo_client:
            try:
                db = self._mongo_client[self.config.mongo_db]
                yield db
            except Exception as e:
                logger.error(f"MongoDB connection error: {e}")
                yield None
        else:
            yield None
    
    @asynccontextmanager
    async def get_http_session(self):
        """Get HTTP session"""
        if not self._http_session:
            await self._init_http_pool()
        
        if self._http_session:
            yield self._http_session
        else:
            yield None
    
    async def execute_in_thread(self, func, *args, **kwargs):
        """Execute function in thread pool"""
        if not self._thread_pool:
            self._init_thread_pool()
        
        if self._thread_pool:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self._thread_pool, func, *args, **kwargs)
        else:
            # Fallback to direct execution
            return func(*args, **kwargs)
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on all connections"""
        health_status = {
            'mongodb': {'status': 'unknown', 'error': None},
            'http_pool': {'status': 'unknown', 'error': None},
            'thread_pool': {'status': 'unknown', 'error': None}
        }
        
        # Check MongoDB
        try:
            if self._mongo_client:
                async with self.get_mongo_db() as db:
                    if db:
                        await db.command('ping')
                        health_status['mongodb']['status'] = 'healthy'
                    else:
                        health_status['mongodb']['status'] = 'unhealthy'
                        health_status['mongodb']['error'] = 'No database connection'
            else:
                health_status['mongodb']['status'] = 'unhealthy'
                health_status['mongodb']['error'] = 'MongoDB client not initialized'
        except Exception as e:
            health_status['mongodb']['status'] = 'unhealthy'
            health_status['mongodb']['error'] = str(e)
        
        # Check HTTP pool
        try:
            if self._http_session and not self._http_session.closed:
                health_status['http_pool']['status'] = 'healthy'
            else:
                health_status['http_pool']['status'] = 'unhealthy'
                health_status['http_pool']['error'] = 'HTTP session not available'
        except Exception as e:
            health_status['http_pool']['status'] = 'unhealthy'
            health_status['http_pool']['error'] = str(e)
        
        # Check thread pool
        try:
            if self._thread_pool and not self._thread_pool._shutdown:
                health_status['thread_pool']['status'] = 'healthy'
            else:
                health_status['thread_pool']['status'] = 'unhealthy'
                health_status['thread_pool']['error'] = 'Thread pool not available'
        except Exception as e:
            health_status['thread_pool']['status'] = 'unhealthy'
            health_status['thread_pool']['error'] = str(e)
        
        return health_status
    
    async def close(self):
        """Close all connections"""
        try:
            if self._mongo_client:
                self._mongo_client.close()
                logger.info("MongoDB connection closed")
            
            if self._http_session and not self._http_session.closed:
                await self._http_session.close()
                logger.info("HTTP session closed")
            
            if self._thread_pool:
                self._thread_pool.shutdown(wait=True)
                logger.info("Thread pool closed")
                
        except Exception as e:
            logger.error(f"Error closing connections: {e}")

class SyncConnectionPool:
    """
    Synchronous connection pool for backward compatibility
    """
    
    def __init__(self, config: ConnectionConfig = None):
        self.config = config or ConnectionConfig()
        self._mongo_client = None
        self._lock = threading.Lock()
    
    def get_mongo_client(self) -> Optional[MongoClient]:
        """Get synchronous MongoDB client with connection pooling"""
        if self._mongo_client is None:
            with self._lock:
                if self._mongo_client is None:  # Double-check locking
                    try:
                        mongo_uri = self._build_mongo_uri()
                        self._mongo_client = MongoClient(
                            mongo_uri,
                            maxPoolSize=self.config.mongo_max_pool_size,
                            minPoolSize=self.config.mongo_min_pool_size,
                            maxIdleTimeMS=30000,
                            waitQueueTimeoutMS=5000,
                            serverSelectionTimeoutMS=5000,
                            connectTimeoutMS=10000,
                            socketTimeoutMS=20000,
                            retryWrites=True,
                            retryReads=True
                        )
                        
                        # Test connection
                        self._mongo_client.admin.command('ping')
                        logger.info("MongoDB sync connection pool initialized")
                        
                    except Exception as e:
                        logger.error(f"Failed to initialize sync MongoDB pool: {e}")
                        self._mongo_client = None
        
        return self._mongo_client
    
    def _build_mongo_uri(self) -> str:
        """Build MongoDB connection URI"""
        if "social" in self.config.mongo_user:
            return f"mongodb://{self.config.mongo_user}:{self.config.mongo_password}@{self.config.mongo_host}:{self.config.mongo_port}/{self.config.mongo_db}?compressors=zlib&directConnection=true"
        
        password_encoded = urllib.parse.quote_plus(self.config.mongo_password)
        return f"mongodb://{self.config.mongo_user}:{password_encoded}@{self.config.mongo_host}:{self.config.mongo_port}/{self.config.mongo_db}"
    
    def get_database(self):
        """Get database instance"""
        client = self.get_mongo_client()
        if client:
            return client[self.config.mongo_db]
        return None
    
    def health_check(self) -> Dict[str, Any]:
        """Perform health check"""
        try:
            client = self.get_mongo_client()
            if client:
                client.admin.command('ping')
                return {'status': 'healthy', 'timestamp': time.time()}
            else:
                return {'status': 'unhealthy', 'error': 'No MongoDB connection'}
        except Exception as e:
            return {'status': 'unhealthy', 'error': str(e)}

# Global connection pool instances
async_pool = AsyncConnectionPool()
sync_pool = SyncConnectionPool()

async def initialize_pools():
    """Initialize all connection pools"""
    await async_pool.initialize()
    sync_pool.get_mongo_client()  # Initialize sync pool
    logger.info("All connection pools ready")

async def close_pools():
    """Close all connection pools"""
    await async_pool.close()
    if sync_pool._mongo_client:
        sync_pool._mongo_client.close()
    logger.info("All connection pools closed")
