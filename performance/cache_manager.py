"""
Enterprise-grade caching system for HR Bot performance optimization.
Implements Redis-based caching with TTL, cache warming, and intelligent invalidation.
"""

import redis
import json
import hashlib
import asyncio
from typing import Any, Optional, Dict, List, Union
from datetime import datetime, timedelta
import logging
from functools import wraps
import pickle
import os
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class CacheConfig:
    """Cache configuration settings"""
    redis_host: str = os.getenv("REDIS_HOST", "localhost")
    redis_port: int = int(os.getenv("REDIS_PORT", "6379"))
    redis_db: int = int(os.getenv("REDIS_DB", "0"))
    redis_password: Optional[str] = os.getenv("REDIS_PASSWORD")
    default_ttl: int = int(os.getenv("CACHE_DEFAULT_TTL", "3600"))  # 1 hour
    max_retries: int = 3
    retry_delay: float = 0.1

class CacheManager:
    """
    Enterprise-grade cache manager with Redis backend.
    Supports async operations, automatic serialization, and intelligent cache strategies.
    """
    
    def __init__(self, config: CacheConfig = None):
        self.config = config or CacheConfig()
        self._redis_client = None
        self._async_redis_client = None
        self._connection_pool = None
        
    def _get_redis_client(self) -> redis.Redis:
        """Get synchronous Redis client with connection pooling"""
        if self._redis_client is None:
            try:
                self._connection_pool = redis.ConnectionPool(
                    host=self.config.redis_host,
                    port=self.config.redis_port,
                    db=self.config.redis_db,
                    password=self.config.redis_password,
                    max_connections=20,
                    retry_on_timeout=True,
                    socket_keepalive=True,
                    socket_keepalive_options={},
                    health_check_interval=30
                )
                self._redis_client = redis.Redis(connection_pool=self._connection_pool)
                # Test connection
                self._redis_client.ping()
                logger.info("Redis connection established successfully")
            except Exception as e:
                logger.error(f"Failed to connect to Redis: {e}")
                self._redis_client = None
        return self._redis_client
    
    async def _get_async_redis_client(self):
        """Get asynchronous Redis client"""
        if self._async_redis_client is None:
            try:
                import aioredis
                self._async_redis_client = await aioredis.from_url(
                    f"redis://{self.config.redis_host}:{self.config.redis_port}/{self.config.redis_db}",
                    password=self.config.redis_password,
                    max_connections=20,
                    retry_on_timeout=True
                )
                await self._async_redis_client.ping()
                logger.info("Async Redis connection established successfully")
            except Exception as e:
                logger.error(f"Failed to connect to async Redis: {e}")
                self._async_redis_client = None
        return self._async_redis_client
    
    def _generate_cache_key(self, prefix: str, *args, **kwargs) -> str:
        """Generate consistent cache key from arguments"""
        key_data = {
            'args': args,
            'kwargs': sorted(kwargs.items()) if kwargs else {}
        }
        key_string = json.dumps(key_data, sort_keys=True, default=str)
        key_hash = hashlib.md5(key_string.encode()).hexdigest()
        return f"hr_bot:{prefix}:{key_hash}"
    
    def _serialize_data(self, data: Any) -> bytes:
        """Serialize data for Redis storage"""
        try:
            return pickle.dumps(data)
        except Exception as e:
            logger.error(f"Failed to serialize data: {e}")
            return json.dumps(data, default=str).encode()
    
    def _deserialize_data(self, data: bytes) -> Any:
        """Deserialize data from Redis"""
        try:
            return pickle.loads(data)
        except Exception:
            try:
                return json.loads(data.decode())
            except Exception as e:
                logger.error(f"Failed to deserialize data: {e}")
                return None
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        client = self._get_redis_client()
        if not client:
            return None
        
        try:
            data = client.get(key)
            if data:
                return self._deserialize_data(data)
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache with TTL"""
        client = self._get_redis_client()
        if not client:
            return False
        
        try:
            ttl = ttl or self.config.default_ttl
            serialized_data = self._serialize_data(value)
            return client.setex(key, ttl, serialized_data)
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            return False
    
    async def async_get(self, key: str) -> Optional[Any]:
        """Async get value from cache"""
        client = await self._get_async_redis_client()
        if not client:
            return None
        
        try:
            data = await client.get(key)
            if data:
                return self._deserialize_data(data)
        except Exception as e:
            logger.error(f"Async cache get error for key {key}: {e}")
        return None
    
    async def async_set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Async set value in cache with TTL"""
        client = await self._get_async_redis_client()
        if not client:
            return False
        
        try:
            ttl = ttl or self.config.default_ttl
            serialized_data = self._serialize_data(value)
            return await client.setex(key, ttl, serialized_data)
        except Exception as e:
            logger.error(f"Async cache set error for key {key}: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """Delete key from cache"""
        client = self._get_redis_client()
        if not client:
            return False
        
        try:
            return bool(client.delete(key))
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
            return False
    
    def delete_pattern(self, pattern: str) -> int:
        """Delete all keys matching pattern"""
        client = self._get_redis_client()
        if not client:
            return 0
        
        try:
            keys = client.keys(pattern)
            if keys:
                return client.delete(*keys)
            return 0
        except Exception as e:
            logger.error(f"Cache delete pattern error for {pattern}: {e}")
            return 0
    
    def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        client = self._get_redis_client()
        if not client:
            return False
        
        try:
            return bool(client.exists(key))
        except Exception as e:
            logger.error(f"Cache exists error for key {key}: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        client = self._get_redis_client()
        if not client:
            return {}
        
        try:
            info = client.info()
            return {
                'connected_clients': info.get('connected_clients', 0),
                'used_memory': info.get('used_memory_human', '0B'),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
                'hit_rate': self._calculate_hit_rate(info),
                'total_keys': sum(info.get(f'db{i}', {}).get('keys', 0) for i in range(16))
            }
        except Exception as e:
            logger.error(f"Failed to get cache stats: {e}")
            return {}
    
    def _calculate_hit_rate(self, info: Dict) -> float:
        """Calculate cache hit rate"""
        hits = info.get('keyspace_hits', 0)
        misses = info.get('keyspace_misses', 0)
        total = hits + misses
        return (hits / total * 100) if total > 0 else 0.0
    
    def health_check(self) -> Dict[str, Any]:
        """Perform cache health check"""
        try:
            client = self._get_redis_client()
            if not client:
                return {'status': 'unhealthy', 'error': 'No Redis connection'}
            
            # Test basic operations
            test_key = 'health_check_test'
            test_value = {'timestamp': datetime.now().isoformat()}
            
            # Test set
            set_result = self.set(test_key, test_value, 60)
            if not set_result:
                return {'status': 'unhealthy', 'error': 'Failed to set test key'}
            
            # Test get
            get_result = self.get(test_key)
            if not get_result:
                return {'status': 'unhealthy', 'error': 'Failed to get test key'}
            
            # Test delete
            self.delete(test_key)
            
            return {
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'stats': self.get_stats()
            }
        except Exception as e:
            return {'status': 'unhealthy', 'error': str(e)}

# Global cache manager instance
cache_manager = CacheManager()

def cache_result(prefix: str, ttl: Optional[int] = None, key_func=None):
    """
    Decorator for caching function results
    
    Args:
        prefix: Cache key prefix
        ttl: Time to live in seconds
        key_func: Custom function to generate cache key
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = cache_manager._generate_cache_key(prefix, *args, **kwargs)
            
            # Try to get from cache
            cached_result = cache_manager.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for key: {cache_key}")
                return cached_result
            
            # Execute function and cache result
            logger.debug(f"Cache miss for key: {cache_key}")
            result = func(*args, **kwargs)
            
            # Cache the result
            cache_manager.set(cache_key, result, ttl)
            return result
        
        return wrapper
    return decorator

def async_cache_result(prefix: str, ttl: Optional[int] = None, key_func=None):
    """
    Async decorator for caching function results
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate cache key
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = cache_manager._generate_cache_key(prefix, *args, **kwargs)
            
            # Try to get from cache
            cached_result = await cache_manager.async_get(cache_key)
            if cached_result is not None:
                logger.debug(f"Async cache hit for key: {cache_key}")
                return cached_result
            
            # Execute function and cache result
            logger.debug(f"Async cache miss for key: {cache_key}")
            result = await func(*args, **kwargs)
            
            # Cache the result
            await cache_manager.async_set(cache_key, result, ttl)
            return result
        
        return wrapper
    return decorator
