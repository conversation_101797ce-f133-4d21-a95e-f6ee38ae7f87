"""
Enterprise API routes with enhanced security, validation, and performance.
Implements async processing, comprehensive error handling, and audit logging.
"""

import asyncio
import time
import uuid
from typing import List, Optional, Dict, Any

from fastapi import FastAPI, Request, HTTPException, Header, Depends
from pydantic import BaseModel, validator
import jwt

# Import security and performance components
from security.rbac_manager import rbac_manager, require_permission, Permission
from security.input_validator import hr_validator, validate_input, security_validator
from security.rate_limiter import rate_limiter, rate_limit
from security.audit_logger import audit_logger, audit_log, AuditEventType, AuditSeverity
from performance.cache_manager import cache_manager, cache_result
from performance.connection_pool import async_pool
from performance.async_optimizer import async_tool_executor, async_llm_manager

# Import existing components
from packages import *
from validate_emp import validate_employee
from hr_bot import (
    QwenChatLLM, AgentState, call_model, tool_router, should_continue,
    TOOL_FUNCTIONS, generate_chat_heading, check_session_exists,
    create_new_session, log_chat_interaction, set_emp_id
)
from tool_schema import TOOLS_SCHEMA
import config

logger = logging.getLogger(__name__)

# Enhanced request models with validation
class EnhancedMessage(BaseModel):
    role: str
    content: str
    
    @validator('role')
    def validate_role(cls, v):
        if v not in ['user', 'assistant', 'system']:
            raise ValueError('Role must be user, assistant, or system')
        return v
    
    @validator('content')
    def validate_content(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Content cannot be empty')
        if len(v) > 10000:
            raise ValueError('Content too long (max 10000 characters)')
        return v.strip()

class EnhancedQueryRequest(BaseModel):
    question: str
    session_id: Optional[str] = None
    history: List[EnhancedMessage] = []
    context: Optional[Dict[str, Any]] = {}
    
    @validator('question')
    def validate_question(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Question cannot be empty')
        if len(v) > 1000:
            raise ValueError('Question too long (max 1000 characters)')
        
        # Security validation
        validation_result = security_validator.comprehensive_validate(v, "question")
        if not validation_result["valid"]:
            raise ValueError(f"Security validation failed: {validation_result['threats']}")
        
        return validation_result["sanitized_value"]
    
    @validator('history')
    def validate_history(cls, v):
        if len(v) > 50:  # Limit conversation history
            raise ValueError('History too long (max 50 messages)')
        return v

class TokenValidationResult(BaseModel):
    valid: bool
    emp_id: Optional[str] = None
    error: Optional[str] = None
    payload: Optional[Dict] = None

# Authentication and authorization
async def validate_bearer_token(authorization: str = Header(...)) -> TokenValidationResult:
    """Enhanced token validation with caching"""
    
    # Check cache first
    cache_key = f"token_validation:{hashlib.md5(authorization.encode()).hexdigest()}"
    cached_result = cache_manager.get(cache_key)
    if cached_result:
        return TokenValidationResult(**cached_result)
    
    if not authorization or not authorization.startswith("Bearer "):
        result = TokenValidationResult(
            valid=False,
            error="Invalid authorization header format. Expected 'Bearer <token>'"
        )
    else:
        try:
            from routes.apis import verify_and_decrypt_token
            encrypted_token = authorization.split(" ")[1]
            token_result = verify_and_decrypt_token(encrypted_token)
            
            result = TokenValidationResult(
                valid=token_result["valid"],
                emp_id=token_result.get("emp_id"),
                error=token_result.get("error"),
                payload=token_result.get("payload")
            )
            
            # Cache valid tokens for 5 minutes
            if result.valid:
                cache_manager.set(cache_key, result.dict(), ttl=300)
                
        except Exception as e:
            result = TokenValidationResult(
                valid=False,
                error=f"Token validation error: {str(e)}"
            )
    
    return result

async def get_current_user(token_result: TokenValidationResult = Depends(validate_bearer_token)) -> str:
    """Get current authenticated user"""
    if not token_result.valid:
        raise HTTPException(
            status_code=401,
            detail=token_result.error or "Authentication failed"
        )
    return token_result.emp_id

def get_client_ip(request: Request) -> str:
    """Extract client IP address"""
    x_forwarded_for = request.headers.get('x-forwarded-for')
    if x_forwarded_for:
        return x_forwarded_for.split(',')[0].strip()
    return request.client.host if request.client else "unknown"

# Enhanced HR query processing
@cache_result("employee_validation", ttl=3600)  # Cache for 1 hour
async def validate_employee_cached(emp_id: str) -> Dict[str, Any]:
    """Cached employee validation"""
    return validate_employee(emp_id)

async def process_hr_query_async(
    request: EnhancedQueryRequest,
    emp_id: str,
    session_id: str,
    user_ip: str
) -> Dict[str, Any]:
    """Process HR query with async optimization"""
    
    start_time = time.time()
    
    try:
        # Validate employee (with caching)
        validation = await validate_employee_cached(emp_id)
        if not validation["valid"]:
            raise HTTPException(
                status_code=401,
                detail=f"Employee validation failed: {validation.get('error')}"
            )
        
        # Set global employee ID
        set_emp_id(emp_id)
        
        # Session handling
        session_check = check_session_exists(session_id)
        if not session_check["exists"]:
            heading = generate_chat_heading(request.question, validation.get("data", {}))
            create_new_session(session_id, emp_id, heading, user_ip)
        else:
            heading = session_check["session_data"]["heading"]
        
        # Prepare message history
        messages = []
        for msg in request.history[-10:]:  # Keep last 10 messages
            if msg.role == "user":
                messages.append(HumanMessage(content=msg.content))
            elif msg.role == "assistant":
                messages.append(AIMessage(content=msg.content))
        
        # Add current question
        current_question = f"{request.question} (empcode: {emp_id})"
        messages.append(HumanMessage(content=current_question))
        
        # Create initial state
        initial_state = {
            "messages": messages,
            "employee_context": validation.get("data", {}),
            "payslip_state": {}
        }
        
        # Process with async optimization
        answer = ""
        try:
            # Use the existing graph processing
            from hr_bot import app as hr_app
            for event in hr_app.stream(initial_state, stream_mode="values"):
                last_message = event["messages"][-1]
                if hasattr(last_message, "content"):
                    answer = last_message.content
                    if "</think>" in answer:
                        answer = answer.split("</think>")[-1].strip()
        except Exception as e:
            logger.error(f"HR processing error: {e}")
            answer = "I apologize, but I'm experiencing technical difficulties. Please try again later or contact HR if urgent."
        
        # Clean up answer
        emp_name = validation["data"].get("empname") if validation["data"] else None
        if emp_name and answer:
            answer = answer.strip()
        else:
            answer = "I apologize, but I'm experiencing technical difficulties. Please try again later or contact HR if urgent."
        
        response_time = time.time() - start_time
        
        # Log interaction
        log_chat_interaction(
            session_id=session_id,
            emp_code=emp_id,
            heading=heading,
            user_question=request.question,
            bot_response=answer,
            response_time=response_time,
            status_code=200,
            user_ip=user_ip,
            error_code=0
        )
        
        return {
            "error": 0,
            "emp_id": validation["data"].get("empcode") if validation["data"] else emp_id,
            "session_id": session_id,
            "question": request.question,
            "answer": answer,
            "response_time_seconds": round(response_time, 2),
            "context": {
                "employee_name": emp_name,
                "session_heading": heading
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"HR query processing error: {e}")
        response_time = time.time() - start_time
        
        # Log error
        log_chat_interaction(
            session_id=session_id,
            emp_code=emp_id,
            heading="System Error",
            user_question=request.question,
            bot_response="System error occurred",
            response_time=response_time,
            status_code=500,
            user_ip=user_ip,
            error_code=1,
            error_msg=str(e)
        )
        
        raise HTTPException(
            status_code=500,
            detail="Internal server error occurred while processing your request"
        )

def register_hr_routes(app: FastAPI, bot_instance):
    """Register HR API routes"""
    
    @app.post("/ask_hr")
    @rate_limit("hr_query")
    @audit_log(AuditEventType.DATA_VIEW, AuditSeverity.MEDIUM)
    async def ask_question(
        request: EnhancedQueryRequest,
        http_request: Request,
        current_user: str = Depends(get_current_user)
    ):
        """Enhanced HR question endpoint with async processing"""
        
        user_ip = get_client_ip(http_request)
        
        # Generate session ID if not provided
        session_id = request.session_id or str(uuid.uuid4())
        
        # Check permissions
        if not rbac_manager.has_permission(current_user, Permission.VIEW_HR_POLICIES):
            audit_logger.log_event(
                event_type=AuditEventType.PERMISSION_DENIED,
                severity=AuditSeverity.HIGH,
                resource="ask_hr",
                action="query",
                result="denied",
                user_id=current_user,
                session_id=session_id,
                ip_address=user_ip
            )
            raise HTTPException(status_code=403, detail="Insufficient permissions")
        
        # Process query
        return await process_hr_query_async(request, current_user, session_id, user_ip)
    
    @app.get("/sessions")
    @rate_limit("hr_query")
    async def get_employee_sessions(
        http_request: Request,
        current_user: str = Depends(get_current_user),
        limit: int = 20
    ):
        """Get employee sessions with caching"""
        
        cache_key = f"employee_sessions:{current_user}:{limit}"
        cached_sessions = cache_manager.get(cache_key)
        if cached_sessions:
            return cached_sessions
        
        try:
            async with async_pool.get_mongo_db() as db:
                if db is None:
                    raise HTTPException(status_code=503, detail="Database not available")
                
                sessions_collection = db['hr_chat_sessions']
                sessions = await sessions_collection.find(
                    {"emp_code": current_user}
                ).sort("last_activity", -1).limit(limit).to_list(length=limit)
                
                # Convert ObjectId to string
                for session in sessions:
                    session["_id"] = str(session["_id"])
                
                result = {
                    "error": 0,
                    "emp_code": current_user,
                    "sessions": sessions,
                    "total_sessions": len(sessions)
                }
                
                # Cache for 5 minutes
                cache_manager.set(cache_key, result, ttl=300)
                return result
                
        except Exception as e:
            logger.error(f"Error fetching sessions: {e}")
            raise HTTPException(status_code=500, detail="Failed to fetch sessions")
    
    @app.get("/session/{session_id}")
    @rate_limit("hr_query")
    async def get_session_info(
        session_id: str,
        current_user: str = Depends(get_current_user)
    ):
        """Get session information with access control"""
        
        try:
            session_check = check_session_exists(session_id)
            if session_check["exists"]:
                session_data = session_check["session_data"]
                
                # Check if user owns this session
                if session_data.get("emp_code") != current_user:
                    if not rbac_manager.has_permission(current_user, Permission.VIEW_ALL_EMPLOYEES):
                        raise HTTPException(status_code=403, detail="Access denied to this session")
                
                return {
                    "error": 0,
                    "session_id": session_id,
                    "exists": True,
                    "emp_code": session_data.get("emp_code"),
                    "heading": session_data.get("heading"),
                    "created_at": session_data.get("created_at"),
                    "last_activity": session_data.get("last_activity")
                }
            else:
                return {
                    "error": 0,
                    "session_id": session_id,
                    "exists": False
                }
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error fetching session info: {e}")
            raise HTTPException(status_code=500, detail="Failed to fetch session information")
    
    @app.get("/chat-logs/session/{session_id}")
    @rate_limit("hr_query")
    async def get_session_chat_logs(
        session_id: str,
        current_user: str = Depends(get_current_user),
        limit: int = 50
    ):
        """Get chat logs with access control and caching"""
        
        cache_key = f"chat_logs:{session_id}:{limit}"
        cached_logs = cache_manager.get(cache_key)
        if cached_logs:
            return cached_logs
        
        try:
            async with async_pool.get_mongo_db() as db:
                if db is None:
                    raise HTTPException(status_code=503, detail="Database not available")
                
                # Check session ownership
                sessions_collection = db['hr_chat_sessions']
                session = await sessions_collection.find_one({"session_id": session_id})
                
                if session and session.get("emp_code") != current_user:
                    if not rbac_manager.has_permission(current_user, Permission.VIEW_ALL_EMPLOYEES):
                        raise HTTPException(status_code=403, detail="Access denied to this session")
                
                # Fetch logs
                logs_collection = db['hr_chat_logs']
                logs = await logs_collection.find(
                    {"session_id": session_id}
                ).sort("timestamp", 1).limit(limit).to_list(length=limit)
                
                # Transform logs into chat message format
                chat_messages = []
                for log in logs:
                    chat_messages.append({
                        "role": "user",
                        "message": log["user_question"],
                        "timestamp": log["timestamp"].isoformat() if isinstance(log["timestamp"], datetime) else log["timestamp"]
                    })
                    
                    chat_messages.append({
                        "role": "assistant",
                        "message": log["bot_response"],
                        "timestamp": log["timestamp"].isoformat() if isinstance(log["timestamp"], datetime) else log["timestamp"]
                    })
                
                result = {
                    "error": 0,
                    "session_id": session_id,
                    "data": chat_messages,
                    "total_messages": len(chat_messages),
                    "total_interactions": len(logs)
                }
                
                # Cache for 2 minutes
                cache_manager.set(cache_key, result, ttl=120)
                return result
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error fetching chat logs: {e}")
            raise HTTPException(status_code=500, detail="Failed to fetch chat logs")
    
    @app.get("/analytics/daily-stats")
    @require_permission(Permission.VIEW_ANALYTICS)
    @rate_limit("admin")
    async def get_daily_stats(
        date_str: Optional[str] = None,
        current_user: str = Depends(get_current_user)
    ):
        """Get daily analytics with permission check"""
        
        cache_key = f"daily_stats:{date_str or 'today'}"
        cached_stats = cache_manager.get(cache_key)
        if cached_stats:
            return cached_stats
        
        try:
            async with async_pool.get_mongo_db() as db:
                if db is None:
                    raise HTTPException(status_code=503, detail="Database not available")
                
                if date_str:
                    target_date = datetime.strptime(date_str, "%Y-%m-%d")
                else:
                    target_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                
                next_date = target_date + timedelta(days=1)
                
                logs_collection = db['hr_chat_logs']
                pipeline = [
                    {
                        "$match": {
                            "timestamp": {
                                "$gte": target_date,
                                "$lt": next_date
                            }
                        }
                    },
                    {
                        "$group": {
                            "_id": None,
                            "total_interactions": {"$sum": 1},
                            "unique_employees": {"$addToSet": "$emp_code"},
                            "unique_sessions": {"$addToSet": "$session_id"},
                            "avg_response_time": {"$avg": "$response_time_seconds"},
                            "max_response_time": {"$max": "$response_time_seconds"},
                            "min_response_time": {"$min": "$response_time_seconds"},
                            "successful_interactions": {
                                "$sum": {"$cond": [{"$eq": ["$status_code", 200]}, 1, 0]}
                            },
                            "error_interactions": {
                                "$sum": {"$cond": [{"$ne": ["$status_code", 200]}, 1, 0]}
                            }
                        }
                    }
                ]
                
                result_cursor = logs_collection.aggregate(pipeline)
                results = await result_cursor.to_list(length=1)
                
                if results:
                    stats = results[0]
                    stats["unique_employees_count"] = len(stats["unique_employees"])
                    stats["unique_sessions_count"] = len(stats["unique_sessions"])
                    del stats["unique_employees"]
                    del stats["unique_sessions"]
                    del stats["_id"]
                else:
                    stats = {
                        "total_interactions": 0,
                        "unique_employees_count": 0,
                        "unique_sessions_count": 0,
                        "avg_response_time": 0,
                        "successful_interactions": 0,
                        "error_interactions": 0
                    }
                
                result = {
                    "error": 0,
                    "date": target_date.strftime("%Y-%m-%d"),
                    "stats": stats
                }
                
                # Cache for 1 hour
                cache_manager.set(cache_key, result, ttl=3600)
                return result
                
        except Exception as e:
            logger.error(f"Error fetching daily stats: {e}")
            raise HTTPException(status_code=500, detail="Failed to fetch analytics")
    
    # Import and register authentication routes
    from routes.apis import (
        send_verification, verify_otp, decrypt_emp_token,
        OTPSend, OTPRequest
    )
    
    @app.post("/employee/send-verification")
    @rate_limit("auth")
    async def send_verification_enhanced(request: OTPSend, http_request: Request):
        """Enhanced OTP sending with rate limiting"""
        user_ip = get_client_ip(http_request)
        
        # Validate input
        validation_result = hr_validator.validate_hr_request({"emp_id": request.emp_id})
        if not validation_result["valid"]:
            raise HTTPException(status_code=400, detail=validation_result["errors"])
        
        return send_verification(request)
    
    @app.post("/employee/verify-otp")
    @rate_limit("auth")
    async def verify_otp_enhanced(request: OTPRequest, http_request: Request):
        """Enhanced OTP verification with audit logging"""
        user_ip = get_client_ip(http_request)
        
        # Audit log the attempt
        audit_logger.log_event(
            event_type=AuditEventType.LOGIN_SUCCESS if request.otp else AuditEventType.LOGIN_FAILURE,
            severity=AuditSeverity.MEDIUM,
            resource="verify_otp",
            action="authenticate",
            result="attempt",
            user_id=request.emp_id,
            ip_address=user_ip
        )
        
        return verify_otp(request)
    
    @app.get("/decrypt_emp")
    async def decrypt_emp_enhanced(emp_token: str = Header(...)):
        """Enhanced token decryption"""
        return decrypt_emp_token(emp_token)
