"""
Role-Based Access Control (RBAC) system for HR Bot.
Implements fine-grained permissions, role hierarchies, and access control.
"""

from enum import Enum
from typing import Dict, List, Set, Optional, Any
from dataclasses import dataclass, field
import json
import logging
from datetime import datetime, timedelta
import hashlib
import os

logger = logging.getLogger(__name__)

class Permission(Enum):
    """System permissions"""
    # Basic HR queries
    VIEW_LEAVE_BALANCE = "view_leave_balance"
    VIEW_ATTENDANCE = "view_attendance"
    VIEW_PAYSLIP = "view_payslip"
    VIEW_FORM16 = "view_form16"
    VIEW_MEDICAL_INSURANCE = "view_medical_insurance"
    VIEW_REIMBURSEMENTS = "view_reimbursements"
    VIEW_BUDDY_REFERRALS = "view_buddy_referrals"
    VIEW_PAYOUT_INFO = "view_payout_info"
    VIEW_MRF = "view_mrf"
    
    # Document requests
    REQUEST_PAYSLIP = "request_payslip"
    REQUEST_FORM16 = "request_form16"
    
    # HR policy access
    VIEW_HR_POLICIES = "view_hr_policies"
    
    # Administrative permissions
    VIEW_ALL_EMPLOYEES = "view_all_employees"
    MANAGE_EMPLOYEE_DATA = "manage_employee_data"
    VIEW_ANALYTICS = "view_analytics"
    MANAGE_SYSTEM_CONFIG = "manage_system_config"
    
    # Sensitive operations
    EXPORT_DATA = "export_data"
    DELETE_DATA = "delete_data"
    MODIFY_AUDIT_LOGS = "modify_audit_logs"

class Role(Enum):
    """System roles with hierarchical structure"""
    EMPLOYEE = "employee"
    TEAM_LEAD = "team_lead"
    MANAGER = "manager"
    HR_EXECUTIVE = "hr_executive"
    HR_MANAGER = "hr_manager"
    ADMIN = "admin"
    SUPER_ADMIN = "super_admin"

@dataclass
class RoleDefinition:
    """Role definition with permissions and constraints"""
    name: str
    permissions: Set[Permission]
    inherits_from: Optional[Role] = None
    max_session_duration: int = 8 * 3600  # 8 hours in seconds
    allowed_ip_ranges: List[str] = field(default_factory=list)
    time_restrictions: Dict[str, Any] = field(default_factory=dict)
    data_access_level: str = "self"  # self, team, department, all

class RBACManager:
    """
    Role-Based Access Control Manager
    Handles permissions, role hierarchies, and access control decisions
    """
    
    def __init__(self):
        self.role_definitions = self._initialize_roles()
        self.user_roles = {}  # emp_id -> role mapping
        self.session_permissions = {}  # session_id -> permissions cache
        
    def _initialize_roles(self) -> Dict[Role, RoleDefinition]:
        """Initialize role definitions with permissions"""
        roles = {}
        
        # Employee role - basic permissions
        roles[Role.EMPLOYEE] = RoleDefinition(
            name="Employee",
            permissions={
                Permission.VIEW_LEAVE_BALANCE,
                Permission.VIEW_ATTENDANCE,
                Permission.VIEW_PAYSLIP,
                Permission.VIEW_FORM16,
                Permission.VIEW_MEDICAL_INSURANCE,
                Permission.VIEW_REIMBURSEMENTS,
                Permission.VIEW_BUDDY_REFERRALS,
                Permission.VIEW_PAYOUT_INFO,
                Permission.VIEW_MRF,
                Permission.REQUEST_PAYSLIP,
                Permission.REQUEST_FORM16,
                Permission.VIEW_HR_POLICIES
            },
            data_access_level="self"
        )
        
        # Team Lead - employee permissions + limited team access
        roles[Role.TEAM_LEAD] = RoleDefinition(
            name="Team Lead",
            permissions=roles[Role.EMPLOYEE].permissions | {
                Permission.VIEW_ALL_EMPLOYEES  # Limited to team
            },
            inherits_from=Role.EMPLOYEE,
            data_access_level="team"
        )
        
        # Manager - team lead permissions + department access
        roles[Role.MANAGER] = RoleDefinition(
            name="Manager",
            permissions=roles[Role.TEAM_LEAD].permissions | {
                Permission.MANAGE_EMPLOYEE_DATA,  # Limited to department
                Permission.VIEW_ANALYTICS
            },
            inherits_from=Role.TEAM_LEAD,
            data_access_level="department"
        )
        
        # HR Executive - employee-facing HR operations
        roles[Role.HR_EXECUTIVE] = RoleDefinition(
            name="HR Executive",
            permissions=roles[Role.EMPLOYEE].permissions | {
                Permission.VIEW_ALL_EMPLOYEES,
                Permission.MANAGE_EMPLOYEE_DATA,
                Permission.VIEW_ANALYTICS
            },
            data_access_level="all",
            max_session_duration=12 * 3600  # 12 hours
        )
        
        # HR Manager - full HR operations
        roles[Role.HR_MANAGER] = RoleDefinition(
            name="HR Manager",
            permissions=roles[Role.HR_EXECUTIVE].permissions | {
                Permission.EXPORT_DATA,
                Permission.MANAGE_SYSTEM_CONFIG
            },
            inherits_from=Role.HR_EXECUTIVE,
            data_access_level="all"
        )
        
        # Admin - system administration
        roles[Role.ADMIN] = RoleDefinition(
            name="Admin",
            permissions=roles[Role.HR_MANAGER].permissions | {
                Permission.DELETE_DATA,
                Permission.MODIFY_AUDIT_LOGS
            },
            inherits_from=Role.HR_MANAGER,
            data_access_level="all",
            max_session_duration=4 * 3600  # 4 hours for security
        )
        
        # Super Admin - full system access
        roles[Role.SUPER_ADMIN] = RoleDefinition(
            name="Super Admin",
            permissions=set(Permission),  # All permissions
            inherits_from=Role.ADMIN,
            data_access_level="all",
            max_session_duration=2 * 3600  # 2 hours for maximum security
        )
        
        return roles
    
    def assign_role(self, emp_id: str, role: Role) -> bool:
        """Assign role to employee"""
        try:
            self.user_roles[emp_id] = role
            logger.info(f"Role {role.value} assigned to employee {emp_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to assign role to {emp_id}: {e}")
            return False
    
    def get_user_role(self, emp_id: str) -> Optional[Role]:
        """Get user's role"""
        return self.user_roles.get(emp_id, Role.EMPLOYEE)  # Default to employee
    
    def get_user_permissions(self, emp_id: str) -> Set[Permission]:
        """Get all permissions for a user"""
        role = self.get_user_role(emp_id)
        if role in self.role_definitions:
            return self.role_definitions[role].permissions
        return set()
    
    def has_permission(self, emp_id: str, permission: Permission) -> bool:
        """Check if user has specific permission"""
        user_permissions = self.get_user_permissions(emp_id)
        return permission in user_permissions
    
    def can_access_data(self, emp_id: str, target_emp_id: str, data_type: str) -> bool:
        """Check if user can access another employee's data"""
        role = self.get_user_role(emp_id)
        if role not in self.role_definitions:
            return False
        
        role_def = self.role_definitions[role]
        
        # Self access always allowed
        if emp_id == target_emp_id:
            return True
        
        # Check data access level
        access_level = role_def.data_access_level
        
        if access_level == "self":
            return False
        elif access_level == "team":
            return self._is_same_team(emp_id, target_emp_id)
        elif access_level == "department":
            return self._is_same_department(emp_id, target_emp_id)
        elif access_level == "all":
            return True
        
        return False
    
    def _is_same_team(self, emp_id1: str, emp_id2: str) -> bool:
        """Check if employees are in same team"""
        # This would integrate with your employee database
        # For now, return False as placeholder
        return False
    
    def _is_same_department(self, emp_id1: str, emp_id2: str) -> bool:
        """Check if employees are in same department"""
        # This would integrate with your employee database
        # For now, return False as placeholder
        return False
    
    def validate_session_access(
        self,
        emp_id: str,
        session_id: str,
        ip_address: str,
        session_start: datetime
    ) -> Dict[str, Any]:
        """Validate session access based on role constraints"""
        role = self.get_user_role(emp_id)
        if role not in self.role_definitions:
            return {"valid": False, "reason": "Invalid role"}
        
        role_def = self.role_definitions[role]
        
        # Check session duration
        session_duration = (datetime.now() - session_start).total_seconds()
        if session_duration > role_def.max_session_duration:
            return {"valid": False, "reason": "Session expired"}
        
        # Check IP restrictions (if configured)
        if role_def.allowed_ip_ranges and not self._is_ip_allowed(ip_address, role_def.allowed_ip_ranges):
            return {"valid": False, "reason": "IP not allowed"}
        
        # Check time restrictions (if configured)
        if role_def.time_restrictions and not self._is_time_allowed(role_def.time_restrictions):
            return {"valid": False, "reason": "Access not allowed at this time"}
        
        return {"valid": True, "permissions": list(role_def.permissions)}
    
    def _is_ip_allowed(self, ip_address: str, allowed_ranges: List[str]) -> bool:
        """Check if IP address is in allowed ranges"""
        # Implement IP range checking logic
        # For now, return True as placeholder
        return True
    
    def _is_time_allowed(self, time_restrictions: Dict[str, Any]) -> bool:
        """Check if current time is within allowed hours"""
        # Implement time restriction logic
        # For now, return True as placeholder
        return True
    
    def get_role_hierarchy(self) -> Dict[str, List[str]]:
        """Get role hierarchy for UI display"""
        hierarchy = {}
        for role, definition in self.role_definitions.items():
            hierarchy[role.value] = {
                "name": definition.name,
                "permissions": [p.value for p in definition.permissions],
                "inherits_from": definition.inherits_from.value if definition.inherits_from else None,
                "data_access_level": definition.data_access_level
            }
        return hierarchy
    
    def audit_permission_check(
        self,
        emp_id: str,
        permission: Permission,
        resource: str,
        result: bool,
        ip_address: str = None
    ):
        """Audit permission checks for compliance"""
        audit_entry = {
            "timestamp": datetime.now().isoformat(),
            "emp_id": emp_id,
            "permission": permission.value,
            "resource": resource,
            "result": result,
            "ip_address": ip_address,
            "role": self.get_user_role(emp_id).value if self.get_user_role(emp_id) else None
        }
        
        # Log to audit system
        logger.info(f"Permission audit: {json.dumps(audit_entry)}")
        
        # Here you would also store to audit database
        # self._store_audit_entry(audit_entry)

# Global RBAC manager instance
rbac_manager = RBACManager()

def require_permission(permission: Permission):
    """Decorator to require specific permission"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Extract emp_id from function arguments or context
            emp_id = kwargs.get('emp_id') or getattr(args[0], 'emp_id', None) if args else None
            
            if not emp_id:
                raise PermissionError("Employee ID not found")
            
            if not rbac_manager.has_permission(emp_id, permission):
                rbac_manager.audit_permission_check(emp_id, permission, func.__name__, False)
                raise PermissionError(f"Permission {permission.value} required")
            
            rbac_manager.audit_permission_check(emp_id, permission, func.__name__, True)
            return func(*args, **kwargs)
        
        return wrapper
    return decorator

def check_data_access(target_emp_id_param: str = 'target_emp_id'):
    """Decorator to check data access permissions"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            emp_id = kwargs.get('emp_id') or getattr(args[0], 'emp_id', None) if args else None
            target_emp_id = kwargs.get(target_emp_id_param)
            
            if not emp_id:
                raise PermissionError("Employee ID not found")
            
            if target_emp_id and not rbac_manager.can_access_data(emp_id, target_emp_id, func.__name__):
                raise PermissionError("Access to employee data not allowed")
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator
