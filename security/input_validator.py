"""
Comprehensive input validation and sanitization for HR Bot.
Implements security-first validation with XSS protection, SQL injection prevention,
and business rule validation.
"""

import re
import html
import json
import logging
from typing import Any, Dict, List, Optional, Union, Tuple
from datetime import datetime, date
from dataclasses import dataclass
from enum import Enum
import bleach
import validators
from pydantic import BaseModel, validator, ValidationError

logger = logging.getLogger(__name__)

class ValidationLevel(Enum):
    """Validation strictness levels"""
    BASIC = "basic"
    STRICT = "strict"
    PARANOID = "paranoid"

@dataclass
class ValidationRule:
    """Validation rule definition"""
    field_name: str
    required: bool = False
    min_length: Optional[int] = None
    max_length: Optional[int] = None
    pattern: Optional[str] = None
    allowed_values: Optional[List[str]] = None
    custom_validator: Optional[callable] = None

class SecurityValidator:
    """
    Enterprise-grade input validator with security focus
    """
    
    # Dangerous patterns to detect
    XSS_PATTERNS = [
        r'<script[^>]*>.*?</script>',
        r'javascript:',
        r'on\w+\s*=',
        r'<iframe[^>]*>.*?</iframe>',
        r'<object[^>]*>.*?</object>',
        r'<embed[^>]*>.*?</embed>',
        r'<link[^>]*>',
        r'<meta[^>]*>',
        r'<style[^>]*>.*?</style>',
        r'expression\s*\(',
        r'url\s*\(',
        r'@import',
        r'vbscript:',
        r'data:text/html'
    ]
    
    SQL_INJECTION_PATTERNS = [
        r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)',
        r'(\b(OR|AND)\s+\d+\s*=\s*\d+)',
        r'(\b(OR|AND)\s+[\'"][^\'"]*[\'"])',
        r'(--|#|/\*|\*/)',
        r'(\bUNION\s+SELECT\b)',
        r'(\bINTO\s+OUTFILE\b)',
        r'(\bLOAD_FILE\s*\()',
        r'(\bINTO\s+DUMPFILE\b)'
    ]
    
    COMMAND_INJECTION_PATTERNS = [
        r'[;&|`$(){}[\]\\]',
        r'\b(cat|ls|pwd|whoami|id|uname|ps|netstat|ifconfig|ping|wget|curl|nc|telnet|ssh|ftp)\b',
        r'(\.\.\/|\.\.\\)',
        r'(\/etc\/passwd|\/etc\/shadow|\/proc\/)',
        r'(\$\{|\$\()',
        r'(`[^`]*`)'
    ]
    
    def __init__(self, level: ValidationLevel = ValidationLevel.STRICT):
        self.level = level
        self.allowed_html_tags = ['b', 'i', 'u', 'em', 'strong', 'p', 'br']
        self.allowed_html_attributes = {}
    
    def sanitize_string(self, value: str, allow_html: bool = False) -> str:
        """Sanitize string input"""
        if not isinstance(value, str):
            value = str(value)
        
        # Remove null bytes
        value = value.replace('\x00', '')
        
        # Normalize whitespace
        value = re.sub(r'\s+', ' ', value).strip()
        
        if allow_html:
            # Use bleach to clean HTML
            value = bleach.clean(
                value,
                tags=self.allowed_html_tags,
                attributes=self.allowed_html_attributes,
                strip=True
            )
        else:
            # Escape HTML entities
            value = html.escape(value)
        
        return value
    
    def detect_xss(self, value: str) -> Tuple[bool, List[str]]:
        """Detect XSS attempts"""
        threats = []
        value_lower = value.lower()
        
        for pattern in self.XSS_PATTERNS:
            if re.search(pattern, value_lower, re.IGNORECASE | re.DOTALL):
                threats.append(f"XSS pattern detected: {pattern}")
        
        return len(threats) > 0, threats
    
    def detect_sql_injection(self, value: str) -> Tuple[bool, List[str]]:
        """Detect SQL injection attempts"""
        threats = []
        value_upper = value.upper()
        
        for pattern in self.SQL_INJECTION_PATTERNS:
            if re.search(pattern, value_upper, re.IGNORECASE):
                threats.append(f"SQL injection pattern detected: {pattern}")
        
        return len(threats) > 0, threats
    
    def detect_command_injection(self, value: str) -> Tuple[bool, List[str]]:
        """Detect command injection attempts"""
        threats = []
        
        for pattern in self.COMMAND_INJECTION_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                threats.append(f"Command injection pattern detected: {pattern}")
        
        return len(threats) > 0, threats
    
    def validate_employee_id(self, emp_id: str) -> Tuple[bool, str]:
        """Validate employee ID format"""
        if not emp_id:
            return False, "Employee ID is required"
        
        # Remove any whitespace
        emp_id = emp_id.strip()
        
        # Check if it's numeric and has appropriate length
        if not re.match(r'^\d{4,10}$', emp_id):
            return False, "Employee ID must be 4-10 digits"
        
        return True, emp_id
    
    def validate_email(self, email: str) -> Tuple[bool, str]:
        """Validate email format"""
        if not email:
            return False, "Email is required"
        
        email = email.strip().lower()
        
        if not validators.email(email):
            return False, "Invalid email format"
        
        # Check for suspicious patterns
        if re.search(r'[<>"\']', email):
            return False, "Email contains invalid characters"
        
        return True, email
    
    def validate_date_string(self, date_str: str, format_str: str = "%Y-%m") -> Tuple[bool, str]:
        """Validate date string format"""
        if not date_str:
            return False, "Date is required"
        
        try:
            datetime.strptime(date_str, format_str)
            return True, date_str
        except ValueError:
            return False, f"Invalid date format. Expected: {format_str}"
    
    def validate_session_id(self, session_id: str) -> Tuple[bool, str]:
        """Validate session ID format"""
        if not session_id:
            return False, "Session ID is required"
        
        # UUID format validation
        uuid_pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
        if not re.match(uuid_pattern, session_id, re.IGNORECASE):
            return False, "Invalid session ID format"
        
        return True, session_id
    
    def validate_json_payload(self, payload: str, max_size: int = 10240) -> Tuple[bool, Union[Dict, str]]:
        """Validate JSON payload"""
        if not payload:
            return False, "Payload is required"
        
        # Check size
        if len(payload) > max_size:
            return False, f"Payload too large. Max size: {max_size} bytes"
        
        try:
            parsed = json.loads(payload)
            return True, parsed
        except json.JSONDecodeError as e:
            return False, f"Invalid JSON: {str(e)}"
    
    def validate_file_path(self, file_path: str) -> Tuple[bool, str]:
        """Validate file path for security"""
        if not file_path:
            return False, "File path is required"
        
        # Check for path traversal
        if '..' in file_path or file_path.startswith('/'):
            return False, "Invalid file path"
        
        # Check for suspicious characters
        if re.search(r'[<>:"|?*]', file_path):
            return False, "File path contains invalid characters"
        
        return True, file_path
    
    def comprehensive_validate(self, value: str, field_name: str = "input") -> Dict[str, Any]:
        """Perform comprehensive validation"""
        result = {
            "valid": True,
            "sanitized_value": value,
            "threats": [],
            "warnings": []
        }
        
        if not isinstance(value, str):
            value = str(value)
        
        # Security threat detection
        xss_detected, xss_threats = self.detect_xss(value)
        if xss_detected:
            result["threats"].extend(xss_threats)
            result["valid"] = False
        
        sql_detected, sql_threats = self.detect_sql_injection(value)
        if sql_detected:
            result["threats"].extend(sql_threats)
            result["valid"] = False
        
        cmd_detected, cmd_threats = self.detect_command_injection(value)
        if cmd_detected:
            result["threats"].extend(cmd_threats)
            result["valid"] = False
        
        # Sanitize if no threats detected
        if result["valid"]:
            result["sanitized_value"] = self.sanitize_string(value)
        
        # Log security events
        if result["threats"]:
            logger.warning(f"Security threats detected in {field_name}: {result['threats']}")
        
        return result

class HRInputValidator:
    """
    HR-specific input validator with business rule validation
    """
    
    def __init__(self):
        self.security_validator = SecurityValidator()
        self.hr_validation_rules = self._initialize_hr_rules()
    
    def _initialize_hr_rules(self) -> Dict[str, ValidationRule]:
        """Initialize HR-specific validation rules"""
        return {
            "emp_id": ValidationRule(
                field_name="emp_id",
                required=True,
                min_length=4,
                max_length=10,
                pattern=r'^\d+$'
            ),
            "question": ValidationRule(
                field_name="question",
                required=True,
                min_length=3,
                max_length=1000
            ),
            "session_id": ValidationRule(
                field_name="session_id",
                required=False,
                pattern=r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
            ),
            "email_type": ValidationRule(
                field_name="email_type",
                required=True,
                allowed_values=["personal_email", "official_email", "both_emails"]
            ),
            "months": ValidationRule(
                field_name="months",
                required=True,
                custom_validator=self._validate_month_list
            ),
            "years": ValidationRule(
                field_name="years",
                required=True,
                custom_validator=self._validate_year_list
            ),
            "form_types": ValidationRule(
                field_name="form_types",
                required=True,
                allowed_values=["part_a", "part_b", "tax_comp"]
            )
        }
    
    def _validate_month_list(self, months: List[str]) -> Tuple[bool, str]:
        """Validate list of months in YYYY-MM format"""
        if not isinstance(months, list):
            return False, "Months must be a list"
        
        if len(months) == 0:
            return False, "At least one month is required"
        
        if len(months) > 12:
            return False, "Maximum 12 months allowed"
        
        for month in months:
            valid, error = self.security_validator.validate_date_string(month, "%Y-%m")
            if not valid:
                return False, f"Invalid month format: {month}. {error}"
        
        return True, "Valid"
    
    def _validate_year_list(self, years: List[str]) -> Tuple[bool, str]:
        """Validate list of financial years in YYYY-YYYY format"""
        if not isinstance(years, list):
            return False, "Years must be a list"
        
        if len(years) == 0:
            return False, "At least one year is required"
        
        if len(years) > 5:
            return False, "Maximum 5 years allowed"
        
        for year in years:
            if not re.match(r'^\d{4}-\d{4}$', year):
                return False, f"Invalid year format: {year}. Expected YYYY-YYYY"
            
            start_year, end_year = year.split('-')
            if int(end_year) - int(start_year) != 1:
                return False, f"Invalid financial year: {year}"
        
        return True, "Valid"
    
    def validate_hr_request(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate HR request data"""
        result = {
            "valid": True,
            "errors": [],
            "sanitized_data": {},
            "security_threats": []
        }
        
        for field_name, value in data.items():
            if field_name in self.hr_validation_rules:
                rule = self.hr_validation_rules[field_name]
                
                # Check if required field is present
                if rule.required and (value is None or value == ""):
                    result["errors"].append(f"{field_name} is required")
                    result["valid"] = False
                    continue
                
                # Skip validation for optional empty fields
                if not rule.required and (value is None or value == ""):
                    continue
                
                # Convert to string for validation
                str_value = str(value) if value is not None else ""
                
                # Security validation
                security_result = self.security_validator.comprehensive_validate(str_value, field_name)
                if not security_result["valid"]:
                    result["security_threats"].extend(security_result["threats"])
                    result["valid"] = False
                    continue
                
                # Length validation
                if rule.min_length and len(str_value) < rule.min_length:
                    result["errors"].append(f"{field_name} must be at least {rule.min_length} characters")
                    result["valid"] = False
                    continue
                
                if rule.max_length and len(str_value) > rule.max_length:
                    result["errors"].append(f"{field_name} must be at most {rule.max_length} characters")
                    result["valid"] = False
                    continue
                
                # Pattern validation
                if rule.pattern and not re.match(rule.pattern, str_value):
                    result["errors"].append(f"{field_name} format is invalid")
                    result["valid"] = False
                    continue
                
                # Allowed values validation
                if rule.allowed_values and str_value not in rule.allowed_values:
                    result["errors"].append(f"{field_name} must be one of: {', '.join(rule.allowed_values)}")
                    result["valid"] = False
                    continue
                
                # Custom validation
                if rule.custom_validator:
                    custom_valid, custom_error = rule.custom_validator(value)
                    if not custom_valid:
                        result["errors"].append(f"{field_name}: {custom_error}")
                        result["valid"] = False
                        continue
                
                # Store sanitized value
                result["sanitized_data"][field_name] = security_result["sanitized_value"]
            else:
                # Unknown field - sanitize and include
                if isinstance(value, str):
                    security_result = self.security_validator.comprehensive_validate(value, field_name)
                    if security_result["valid"]:
                        result["sanitized_data"][field_name] = security_result["sanitized_value"]
                    else:
                        result["security_threats"].extend(security_result["threats"])
                        result["valid"] = False
                else:
                    result["sanitized_data"][field_name] = value
        
        return result

# Global validator instances
security_validator = SecurityValidator()
hr_validator = HRInputValidator()

def validate_input(validation_type: str = "hr"):
    """Decorator for input validation"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Extract request data
            request_data = kwargs.get('request') or (args[0] if args else None)
            
            if hasattr(request_data, 'dict'):
                data = request_data.dict()
            elif isinstance(request_data, dict):
                data = request_data
            else:
                raise ValueError("Unable to extract request data for validation")
            
            # Validate based on type
            if validation_type == "hr":
                validation_result = hr_validator.validate_hr_request(data)
            else:
                # Basic security validation
                validation_result = {"valid": True, "sanitized_data": data, "errors": [], "security_threats": []}
                for key, value in data.items():
                    if isinstance(value, str):
                        security_result = security_validator.comprehensive_validate(value, key)
                        if not security_result["valid"]:
                            validation_result["valid"] = False
                            validation_result["security_threats"].extend(security_result["threats"])
            
            if not validation_result["valid"]:
                error_msg = "Validation failed: " + "; ".join(
                    validation_result["errors"] + validation_result["security_threats"]
                )
                raise ValueError(error_msg)
            
            # Replace request data with sanitized data
            if hasattr(request_data, 'dict'):
                for key, value in validation_result["sanitized_data"].items():
                    setattr(request_data, key, value)
            else:
                kwargs['request'] = validation_result["sanitized_data"]
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator
