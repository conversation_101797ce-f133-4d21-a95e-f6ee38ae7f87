"""
Enterprise-grade rate limiting and DDoS protection for HR Bot.
Implements sliding window, token bucket, and adaptive rate limiting algorithms.
"""

import time
import asyncio
import redis
from typing import Dict, Optional, Tuple, List
from dataclasses import dataclass
from enum import Enum
import logging
import json
from datetime import datetime, timedelta
import hashlib
import os

logger = logging.getLogger(__name__)

class RateLimitType(Enum):
    """Rate limit types"""
    PER_USER = "per_user"
    PER_IP = "per_ip"
    PER_ENDPOINT = "per_endpoint"
    GLOBAL = "global"

class RateLimitAlgorithm(Enum):
    """Rate limiting algorithms"""
    SLIDING_WINDOW = "sliding_window"
    TOKEN_BUCKET = "token_bucket"
    FIXED_WINDOW = "fixed_window"

@dataclass
class RateLimit:
    """Rate limit configuration"""
    requests: int  # Number of requests
    window: int    # Time window in seconds
    algorithm: RateLimitAlgorithm = RateLimitAlgorithm.SLIDING_WINDOW
    burst_allowance: int = 0  # Additional burst requests
    block_duration: int = 300  # Block duration in seconds when limit exceeded

@dataclass
class RateLimitResult:
    """Rate limit check result"""
    allowed: bool
    remaining: int
    reset_time: int
    retry_after: Optional[int] = None
    blocked_until: Optional[int] = None

class RedisRateLimiter:
    """
    Redis-based rate limiter with multiple algorithms
    """
    
    def __init__(self, redis_client: redis.Redis = None):
        self.redis_client = redis_client or self._create_redis_client()
        self.rate_limits = self._initialize_rate_limits()
    
    def _create_redis_client(self) -> redis.Redis:
        """Create Redis client for rate limiting"""
        try:
            client = redis.Redis(
                host=os.getenv("REDIS_HOST", "localhost"),
                port=int(os.getenv("REDIS_PORT", "6379")),
                db=int(os.getenv("REDIS_RATE_LIMIT_DB", "1")),
                password=os.getenv("REDIS_PASSWORD"),
                decode_responses=True
            )
            client.ping()
            return client
        except Exception as e:
            logger.error(f"Failed to connect to Redis for rate limiting: {e}")
            return None
    
    def _initialize_rate_limits(self) -> Dict[str, Dict[RateLimitType, RateLimit]]:
        """Initialize rate limit configurations"""
        return {
            # Authentication endpoints
            "auth": {
                RateLimitType.PER_IP: RateLimit(requests=5, window=300),  # 5 per 5 minutes
                RateLimitType.PER_USER: RateLimit(requests=10, window=300)  # 10 per 5 minutes
            },
            
            # HR query endpoints
            "hr_query": {
                RateLimitType.PER_USER: RateLimit(requests=100, window=3600, burst_allowance=20),  # 100 per hour + 20 burst
                RateLimitType.PER_IP: RateLimit(requests=200, window=3600)  # 200 per hour per IP
            },
            
            # Document request endpoints (more restrictive)
            "document_request": {
                RateLimitType.PER_USER: RateLimit(requests=10, window=3600),  # 10 per hour
                RateLimitType.PER_IP: RateLimit(requests=20, window=3600)  # 20 per hour per IP
            },
            
            # Admin endpoints (very restrictive)
            "admin": {
                RateLimitType.PER_USER: RateLimit(requests=50, window=3600),  # 50 per hour
                RateLimitType.PER_IP: RateLimit(requests=100, window=3600)  # 100 per hour per IP
            },
            
            # Global limits
            "global": {
                RateLimitType.GLOBAL: RateLimit(requests=10000, window=3600)  # 10k requests per hour globally
            }
        }
    
    def _get_key(self, limit_type: RateLimitType, identifier: str, endpoint: str) -> str:
        """Generate Redis key for rate limit"""
        return f"rate_limit:{limit_type.value}:{endpoint}:{identifier}"
    
    def _sliding_window_check(
        self,
        key: str,
        limit: RateLimit,
        current_time: int
    ) -> RateLimitResult:
        """Sliding window rate limit check"""
        if not self.redis_client:
            return RateLimitResult(allowed=True, remaining=limit.requests, reset_time=current_time + limit.window)
        
        try:
            pipe = self.redis_client.pipeline()
            
            # Remove expired entries
            pipe.zremrangebyscore(key, 0, current_time - limit.window)
            
            # Count current requests
            pipe.zcard(key)
            
            # Add current request
            pipe.zadd(key, {str(current_time): current_time})
            
            # Set expiry
            pipe.expire(key, limit.window)
            
            results = pipe.execute()
            current_count = results[1]
            
            if current_count <= limit.requests + limit.burst_allowance:
                remaining = max(0, limit.requests - current_count)
                reset_time = current_time + limit.window
                return RateLimitResult(allowed=True, remaining=remaining, reset_time=reset_time)
            else:
                # Remove the request we just added since it's not allowed
                self.redis_client.zrem(key, str(current_time))
                
                # Block the user/IP
                block_key = f"{key}:blocked"
                self.redis_client.setex(block_key, limit.block_duration, current_time)
                
                return RateLimitResult(
                    allowed=False,
                    remaining=0,
                    reset_time=current_time + limit.window,
                    retry_after=limit.block_duration,
                    blocked_until=current_time + limit.block_duration
                )
        
        except Exception as e:
            logger.error(f"Rate limit check failed: {e}")
            # Fail open for availability
            return RateLimitResult(allowed=True, remaining=limit.requests, reset_time=current_time + limit.window)
    
    def _token_bucket_check(
        self,
        key: str,
        limit: RateLimit,
        current_time: int
    ) -> RateLimitResult:
        """Token bucket rate limit check"""
        if not self.redis_client:
            return RateLimitResult(allowed=True, remaining=limit.requests, reset_time=current_time + limit.window)
        
        try:
            bucket_key = f"{key}:bucket"
            
            # Get current bucket state
            bucket_data = self.redis_client.hmget(bucket_key, ['tokens', 'last_refill'])
            tokens = float(bucket_data[0]) if bucket_data[0] else limit.requests
            last_refill = float(bucket_data[1]) if bucket_data[1] else current_time
            
            # Calculate tokens to add
            time_passed = current_time - last_refill
            tokens_to_add = (time_passed / limit.window) * limit.requests
            tokens = min(limit.requests + limit.burst_allowance, tokens + tokens_to_add)
            
            if tokens >= 1:
                # Consume token
                tokens -= 1
                
                # Update bucket
                pipe = self.redis_client.pipeline()
                pipe.hmset(bucket_key, {'tokens': tokens, 'last_refill': current_time})
                pipe.expire(bucket_key, limit.window * 2)
                pipe.execute()
                
                return RateLimitResult(
                    allowed=True,
                    remaining=int(tokens),
                    reset_time=current_time + int((limit.requests - tokens) * (limit.window / limit.requests))
                )
            else:
                # No tokens available
                block_key = f"{key}:blocked"
                self.redis_client.setex(block_key, limit.block_duration, current_time)
                
                return RateLimitResult(
                    allowed=False,
                    remaining=0,
                    reset_time=current_time + int((1 - tokens) * (limit.window / limit.requests)),
                    retry_after=limit.block_duration,
                    blocked_until=current_time + limit.block_duration
                )
        
        except Exception as e:
            logger.error(f"Token bucket check failed: {e}")
            return RateLimitResult(allowed=True, remaining=limit.requests, reset_time=current_time + limit.window)
    
    def _is_blocked(self, key: str) -> Tuple[bool, Optional[int]]:
        """Check if identifier is currently blocked"""
        if not self.redis_client:
            return False, None
        
        try:
            block_key = f"{key}:blocked"
            blocked_until = self.redis_client.get(block_key)
            if blocked_until:
                return True, int(blocked_until)
            return False, None
        except Exception as e:
            logger.error(f"Block check failed: {e}")
            return False, None
    
    def check_rate_limit(
        self,
        endpoint: str,
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> Dict[RateLimitType, RateLimitResult]:
        """Check rate limits for all applicable types"""
        current_time = int(time.time())
        results = {}
        
        # Get rate limits for endpoint
        endpoint_limits = self.rate_limits.get(endpoint, {})
        
        for limit_type, limit_config in endpoint_limits.items():
            # Determine identifier
            if limit_type == RateLimitType.PER_USER and user_id:
                identifier = user_id
            elif limit_type == RateLimitType.PER_IP and ip_address:
                identifier = ip_address
            elif limit_type == RateLimitType.PER_ENDPOINT:
                identifier = endpoint
            elif limit_type == RateLimitType.GLOBAL:
                identifier = "global"
            else:
                continue
            
            key = self._get_key(limit_type, identifier, endpoint)
            
            # Check if blocked
            is_blocked, blocked_until = self._is_blocked(key)
            if is_blocked:
                results[limit_type] = RateLimitResult(
                    allowed=False,
                    remaining=0,
                    reset_time=blocked_until + limit_config.window,
                    retry_after=blocked_until - current_time,
                    blocked_until=blocked_until
                )
                continue
            
            # Apply rate limiting algorithm
            if limit_config.algorithm == RateLimitAlgorithm.SLIDING_WINDOW:
                result = self._sliding_window_check(key, limit_config, current_time)
            elif limit_config.algorithm == RateLimitAlgorithm.TOKEN_BUCKET:
                result = self._token_bucket_check(key, limit_config, current_time)
            else:
                # Default to sliding window
                result = self._sliding_window_check(key, limit_config, current_time)
            
            results[limit_type] = result
        
        return results
    
    def is_request_allowed(
        self,
        endpoint: str,
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> Tuple[bool, Dict[str, any]]:
        """Check if request is allowed under all rate limits"""
        rate_limit_results = self.check_rate_limit(endpoint, user_id, ip_address)
        
        # Check if any rate limit is exceeded
        for limit_type, result in rate_limit_results.items():
            if not result.allowed:
                return False, {
                    "error": "Rate limit exceeded",
                    "limit_type": limit_type.value,
                    "retry_after": result.retry_after,
                    "blocked_until": result.blocked_until,
                    "reset_time": result.reset_time
                }
        
        # Return the most restrictive remaining count
        min_remaining = min((r.remaining for r in rate_limit_results.values()), default=0)
        min_reset_time = min((r.reset_time for r in rate_limit_results.values()), default=int(time.time()) + 3600)
        
        return True, {
            "remaining": min_remaining,
            "reset_time": min_reset_time,
            "rate_limits": {lt.value: {"remaining": r.remaining, "reset_time": r.reset_time} 
                          for lt, r in rate_limit_results.items()}
        }
    
    def get_rate_limit_headers(
        self,
        endpoint: str,
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> Dict[str, str]:
        """Get rate limit headers for HTTP response"""
        rate_limit_results = self.check_rate_limit(endpoint, user_id, ip_address)
        
        if not rate_limit_results:
            return {}
        
        # Get the most restrictive limits
        min_remaining = min((r.remaining for r in rate_limit_results.values()), default=0)
        min_reset_time = min((r.reset_time for r in rate_limit_results.values()), default=int(time.time()) + 3600)
        
        headers = {
            "X-RateLimit-Remaining": str(min_remaining),
            "X-RateLimit-Reset": str(min_reset_time),
            "X-RateLimit-Reset-After": str(max(0, min_reset_time - int(time.time())))
        }
        
        # Add retry-after header if any limit is exceeded
        retry_after_values = [r.retry_after for r in rate_limit_results.values() if r.retry_after]
        if retry_after_values:
            headers["Retry-After"] = str(min(retry_after_values))
        
        return headers
    
    def reset_rate_limit(
        self,
        endpoint: str,
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> bool:
        """Reset rate limits for identifier (admin function)"""
        if not self.redis_client:
            return False
        
        try:
            endpoint_limits = self.rate_limits.get(endpoint, {})
            
            for limit_type in endpoint_limits.keys():
                if limit_type == RateLimitType.PER_USER and user_id:
                    identifier = user_id
                elif limit_type == RateLimitType.PER_IP and ip_address:
                    identifier = ip_address
                elif limit_type == RateLimitType.PER_ENDPOINT:
                    identifier = endpoint
                elif limit_type == RateLimitType.GLOBAL:
                    identifier = "global"
                else:
                    continue
                
                key = self._get_key(limit_type, identifier, endpoint)
                self.redis_client.delete(key, f"{key}:bucket", f"{key}:blocked")
            
            return True
        except Exception as e:
            logger.error(f"Failed to reset rate limits: {e}")
            return False

# Global rate limiter instance
rate_limiter = RedisRateLimiter()

def rate_limit(endpoint: str):
    """Decorator for rate limiting"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Extract user_id and ip_address from request context
            request = kwargs.get('http_request') or kwargs.get('request')
            user_id = kwargs.get('emp_id')
            ip_address = getattr(request, 'client', {}).get('host') if request else None
            
            # Check rate limits
            allowed, rate_info = rate_limiter.is_request_allowed(endpoint, user_id, ip_address)
            
            if not allowed:
                from fastapi import HTTPException
                raise HTTPException(
                    status_code=429,
                    detail=rate_info,
                    headers=rate_limiter.get_rate_limit_headers(endpoint, user_id, ip_address)
                )
            
            # Add rate limit headers to response
            response = func(*args, **kwargs)
            if hasattr(response, 'headers'):
                headers = rate_limiter.get_rate_limit_headers(endpoint, user_id, ip_address)
                for key, value in headers.items():
                    response.headers[key] = value
            
            return response
        
        return wrapper
    return decorator
