"""
Enterprise-grade audit logging system for HR Bot.
Implements comprehensive audit trails, compliance logging, and security event tracking.
"""

import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib
import os
import asyncio
from pymongo import MongoClient
import motor.motor_asyncio
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

class AuditEventType(Enum):
    """Audit event types"""
    # Authentication events
    LOGIN_SUCCESS = "login_success"
    LOGIN_FAILURE = "login_failure"
    LOGOUT = "logout"
    TOKEN_REFRESH = "token_refresh"
    
    # Data access events
    DATA_VIEW = "data_view"
    DATA_EXPORT = "data_export"
    DATA_MODIFICATION = "data_modification"
    DATA_DELETION = "data_deletion"
    
    # HR operations
    PAYSLIP_REQUEST = "payslip_request"
    FORM16_REQUEST = "form16_request"
    LEAVE_QUERY = "leave_query"
    ATTENDANCE_QUERY = "attendance_query"
    
    # Security events
    PERMISSION_DENIED = "permission_denied"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    SECURITY_VIOLATION = "security_violation"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    
    # System events
    SYSTEM_ERROR = "system_error"
    CONFIGURATION_CHANGE = "configuration_change"
    ADMIN_ACTION = "admin_action"

class AuditSeverity(Enum):
    """Audit event severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class AuditEvent:
    """Audit event data structure"""
    event_id: str
    timestamp: datetime
    event_type: AuditEventType
    severity: AuditSeverity
    user_id: Optional[str]
    session_id: Optional[str]
    ip_address: Optional[str]
    user_agent: Optional[str]
    resource: str
    action: str
    result: str  # success, failure, error
    details: Dict[str, Any]
    risk_score: int = 0
    compliance_tags: List[str] = None
    
    def __post_init__(self):
        if self.compliance_tags is None:
            self.compliance_tags = []

class AuditLogger:
    """
    Enterprise audit logging system with multiple storage backends
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or self._default_config()
        self.mongo_client = None
        self.async_mongo_client = None
        self._initialize_storage()
    
    def _default_config(self) -> Dict[str, Any]:
        """Default audit configuration"""
        return {
            "storage": {
                "mongodb": {
                    "enabled": True,
                    "host": os.getenv("MONGO_HOST", "**************"),
                    "port": int(os.getenv("MONGO_PORT", "27017")),
                    "database": os.getenv("MONGO_DB", "jdsocial"),
                    "collection": "audit_logs",
                    "username": os.getenv("MONGO_USER", "jdsocial"),
                    "password": os.getenv("MONGO_PASSWORD", "jdsocial123")
                },
                "file": {
                    "enabled": True,
                    "path": os.getenv("AUDIT_LOG_PATH", "/var/log/hr_bot/audit.log"),
                    "max_size": "100MB",
                    "backup_count": 10
                }
            },
            "retention": {
                "days": int(os.getenv("AUDIT_RETENTION_DAYS", "2555")),  # 7 years
                "archive_after_days": int(os.getenv("AUDIT_ARCHIVE_DAYS", "365"))
            },
            "compliance": {
                "gdpr_enabled": True,
                "pii_masking": True,
                "encryption_enabled": True
            },
            "alerting": {
                "critical_events": True,
                "suspicious_activity_threshold": 5,
                "failed_login_threshold": 3
            }
        }
    
    def _initialize_storage(self):
        """Initialize storage backends"""
        if self.config["storage"]["mongodb"]["enabled"]:
            self._initialize_mongodb()
        
        if self.config["storage"]["file"]["enabled"]:
            self._initialize_file_logging()
    
    def _initialize_mongodb(self):
        """Initialize MongoDB connection for audit logs"""
        try:
            mongo_config = self.config["storage"]["mongodb"]
            
            # Sync client
            connection_string = f"mongodb://{mongo_config['username']}:{mongo_config['password']}@{mongo_config['host']}:{mongo_config['port']}/{mongo_config['database']}"
            self.mongo_client = MongoClient(connection_string)
            
            # Test connection
            self.mongo_client.admin.command('ping')
            
            # Create indexes for performance
            audit_collection = self.mongo_client[mongo_config['database']][mongo_config['collection']]
            audit_collection.create_index([("timestamp", -1)])
            audit_collection.create_index([("user_id", 1)])
            audit_collection.create_index([("event_type", 1)])
            audit_collection.create_index([("severity", 1)])
            audit_collection.create_index([("ip_address", 1)])
            
            logger.info("MongoDB audit storage initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize MongoDB audit storage: {e}")
            self.mongo_client = None
    
    def _initialize_file_logging(self):
        """Initialize file-based audit logging"""
        try:
            log_path = self.config["storage"]["file"]["path"]
            os.makedirs(os.path.dirname(log_path), exist_ok=True)
            
            # Configure file handler
            file_handler = logging.handlers.RotatingFileHandler(
                log_path,
                maxBytes=self._parse_size(self.config["storage"]["file"]["max_size"]),
                backupCount=self.config["storage"]["file"]["backup_count"]
            )
            
            formatter = logging.Formatter(
                '%(asctime)s - AUDIT - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(formatter)
            
            # Create audit logger
            self.file_logger = logging.getLogger('hr_bot_audit')
            self.file_logger.addHandler(file_handler)
            self.file_logger.setLevel(logging.INFO)
            
            logger.info("File audit storage initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize file audit storage: {e}")
            self.file_logger = None
    
    def _parse_size(self, size_str: str) -> int:
        """Parse size string to bytes"""
        size_str = size_str.upper()
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    def _generate_event_id(self, event: AuditEvent) -> str:
        """Generate unique event ID"""
        data = f"{event.timestamp.isoformat()}{event.user_id}{event.event_type.value}{event.resource}"
        return hashlib.sha256(data.encode()).hexdigest()[:16]
    
    def _mask_pii(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Mask PII data for compliance"""
        if not self.config["compliance"]["pii_masking"]:
            return data
        
        masked_data = data.copy()
        pii_fields = ['email', 'phone', 'mobile', 'address', 'ssn', 'pan', 'aadhar']
        
        for field in pii_fields:
            if field in masked_data:
                value = str(masked_data[field])
                if len(value) > 4:
                    masked_data[field] = value[:2] + '*' * (len(value) - 4) + value[-2:]
                else:
                    masked_data[field] = '*' * len(value)
        
        return masked_data
    
    def _calculate_risk_score(self, event: AuditEvent) -> int:
        """Calculate risk score for the event"""
        base_score = 0
        
        # Event type scoring
        risk_scores = {
            AuditEventType.LOGIN_FAILURE: 3,
            AuditEventType.PERMISSION_DENIED: 5,
            AuditEventType.SECURITY_VIOLATION: 8,
            AuditEventType.SUSPICIOUS_ACTIVITY: 9,
            AuditEventType.DATA_DELETION: 7,
            AuditEventType.DATA_EXPORT: 4,
            AuditEventType.ADMIN_ACTION: 6
        }
        
        base_score = risk_scores.get(event.event_type, 1)
        
        # Severity multiplier
        severity_multipliers = {
            AuditSeverity.LOW: 1,
            AuditSeverity.MEDIUM: 2,
            AuditSeverity.HIGH: 3,
            AuditSeverity.CRITICAL: 5
        }
        
        multiplier = severity_multipliers.get(event.severity, 1)
        
        # Result modifier
        if event.result == "failure":
            base_score += 2
        elif event.result == "error":
            base_score += 1
        
        return min(base_score * multiplier, 10)  # Cap at 10
    
    def log_event(
        self,
        event_type: AuditEventType,
        severity: AuditSeverity,
        resource: str,
        action: str,
        result: str,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        compliance_tags: Optional[List[str]] = None
    ) -> str:
        """Log audit event"""
        
        # Create audit event
        event = AuditEvent(
            event_id="",  # Will be generated
            timestamp=datetime.utcnow(),
            event_type=event_type,
            severity=severity,
            user_id=user_id,
            session_id=session_id,
            ip_address=ip_address,
            user_agent=user_agent,
            resource=resource,
            action=action,
            result=result,
            details=self._mask_pii(details or {}),
            compliance_tags=compliance_tags or []
        )
        
        # Generate event ID
        event.event_id = self._generate_event_id(event)
        
        # Calculate risk score
        event.risk_score = self._calculate_risk_score(event)
        
        # Store in MongoDB
        if self.mongo_client:
            try:
                mongo_config = self.config["storage"]["mongodb"]
                collection = self.mongo_client[mongo_config['database']][mongo_config['collection']]
                
                event_dict = asdict(event)
                event_dict['timestamp'] = event.timestamp
                event_dict['event_type'] = event.event_type.value
                event_dict['severity'] = event.severity.value
                
                collection.insert_one(event_dict)
                
            except Exception as e:
                logger.error(f"Failed to store audit event in MongoDB: {e}")
        
        # Store in file
        if hasattr(self, 'file_logger') and self.file_logger:
            try:
                log_entry = {
                    "event_id": event.event_id,
                    "timestamp": event.timestamp.isoformat(),
                    "event_type": event.event_type.value,
                    "severity": event.severity.value,
                    "user_id": event.user_id,
                    "session_id": event.session_id,
                    "ip_address": event.ip_address,
                    "resource": event.resource,
                    "action": event.action,
                    "result": event.result,
                    "risk_score": event.risk_score,
                    "details": event.details
                }
                
                self.file_logger.info(json.dumps(log_entry))
                
            except Exception as e:
                logger.error(f"Failed to store audit event in file: {e}")
        
        # Check for alerting
        self._check_alerting(event)
        
        return event.event_id
    
    def _check_alerting(self, event: AuditEvent):
        """Check if event should trigger alerts"""
        if not self.config["alerting"]["critical_events"]:
            return
        
        # Critical events
        if event.severity == AuditSeverity.CRITICAL:
            self._send_alert(f"Critical audit event: {event.event_type.value}", event)
        
        # High risk score events
        if event.risk_score >= 8:
            self._send_alert(f"High risk audit event: {event.event_type.value}", event)
        
        # Multiple failed logins
        if event.event_type == AuditEventType.LOGIN_FAILURE:
            self._check_failed_login_threshold(event)
    
    def _send_alert(self, message: str, event: AuditEvent):
        """Send security alert"""
        # Implement alerting mechanism (email, Slack, etc.)
        logger.critical(f"SECURITY ALERT: {message} - Event ID: {event.event_id}")
    
    def _check_failed_login_threshold(self, event: AuditEvent):
        """Check failed login threshold"""
        if not self.mongo_client or not event.user_id:
            return
        
        try:
            mongo_config = self.config["storage"]["mongodb"]
            collection = self.mongo_client[mongo_config['database']][mongo_config['collection']]
            
            # Count failed logins in last hour
            one_hour_ago = datetime.utcnow() - timedelta(hours=1)
            failed_count = collection.count_documents({
                "user_id": event.user_id,
                "event_type": AuditEventType.LOGIN_FAILURE.value,
                "timestamp": {"$gte": one_hour_ago}
            })
            
            threshold = self.config["alerting"]["failed_login_threshold"]
            if failed_count >= threshold:
                self._send_alert(
                    f"Multiple failed logins detected for user {event.user_id}: {failed_count} attempts",
                    event
                )
        
        except Exception as e:
            logger.error(f"Failed to check login threshold: {e}")
    
    def search_events(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        user_id: Optional[str] = None,
        event_type: Optional[AuditEventType] = None,
        severity: Optional[AuditSeverity] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Search audit events"""
        if not self.mongo_client:
            return []
        
        try:
            mongo_config = self.config["storage"]["mongodb"]
            collection = self.mongo_client[mongo_config['database']][mongo_config['collection']]
            
            query = {}
            
            if start_time or end_time:
                query["timestamp"] = {}
                if start_time:
                    query["timestamp"]["$gte"] = start_time
                if end_time:
                    query["timestamp"]["$lte"] = end_time
            
            if user_id:
                query["user_id"] = user_id
            
            if event_type:
                query["event_type"] = event_type.value
            
            if severity:
                query["severity"] = severity.value
            
            cursor = collection.find(query).sort("timestamp", -1).limit(limit)
            return list(cursor)
        
        except Exception as e:
            logger.error(f"Failed to search audit events: {e}")
            return []

# Global audit logger instance
audit_logger = AuditLogger()

def audit_log(event_type: AuditEventType, severity: AuditSeverity = AuditSeverity.MEDIUM):
    """Decorator for automatic audit logging"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = datetime.utcnow()
            
            try:
                result = func(*args, **kwargs)
                
                # Extract context information
                user_id = kwargs.get('emp_id')
                session_id = kwargs.get('session_id')
                ip_address = getattr(kwargs.get('http_request'), 'client', {}).get('host') if kwargs.get('http_request') else None
                
                # Log successful execution
                audit_logger.log_event(
                    event_type=event_type,
                    severity=severity,
                    resource=func.__name__,
                    action="execute",
                    result="success",
                    user_id=user_id,
                    session_id=session_id,
                    ip_address=ip_address,
                    details={
                        "function": func.__name__,
                        "execution_time": (datetime.utcnow() - start_time).total_seconds()
                    }
                )
                
                return result
                
            except Exception as e:
                # Log failed execution
                user_id = kwargs.get('emp_id')
                session_id = kwargs.get('session_id')
                ip_address = getattr(kwargs.get('http_request'), 'client', {}).get('host') if kwargs.get('http_request') else None
                
                audit_logger.log_event(
                    event_type=event_type,
                    severity=AuditSeverity.HIGH,
                    resource=func.__name__,
                    action="execute",
                    result="error",
                    user_id=user_id,
                    session_id=session_id,
                    ip_address=ip_address,
                    details={
                        "function": func.__name__,
                        "error": str(e),
                        "execution_time": (datetime.utcnow() - start_time).total_seconds()
                    }
                )
                
                raise
        
        return wrapper
    return decorator
