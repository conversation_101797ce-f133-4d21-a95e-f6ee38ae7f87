version: '3.8'

services:
  # Main HR Bot Application
  hr-bot:
    build:
      context: ..
      dockerfile: deployment/Dockerfile
    container_name: enterprise-hr-bot
    ports:
      - "5011:5011"
    environment:
      - DEBUG=false
      - API_PORT=5011
      - WORKERS=4
      - MAX_REQUESTS=1000
      - MAX_CONCURRENT_REQUESTS=100
      - ENABLE_CACHING=true
      - ENABLE_ASYNC=true
      
      # Database Configuration
      - MONGO_HOST=mongodb
      - MONGO_PORT=27017
      - MONGO_DB=jdsocial
      - MONGO_USER=jdsocial
      - MONGO_PASSWORD=jdsocial123
      
      # Redis Configuration
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis_password
      - REDIS_CACHE_DB=0
      - REDIS_RATE_LIMIT_DB=1
      
      # Security Configuration
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - ALLOWED_HOSTS=*
      - CORS_ORIGINS=*
      
      # Audit Configuration
      - AUDIT_RETENTION_DAYS=2555
      - AUDIT_ARCHIVE_DAYS=365
      - AUDIT_LOG_PATH=/var/log/hr_bot/audit.log
      
      # LLM Configuration
      - QWEN_API_KEY=${QWEN_API_KEY}
      - QWEN_MODEL_NAME=qwen-plus
      
    volumes:
      - hr_bot_logs:/var/log/hr_bot
      - hr_bot_data:/app/data
    depends_on:
      - mongodb
      - redis
    networks:
      - hr_bot_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5011/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MongoDB Database
  mongodb:
    image: mongo:6.0
    container_name: hr-bot-mongodb
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=admin_password
      - MONGO_INITDB_DATABASE=jdsocial
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
      - ./mongodb/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - hr_bot_network
    restart: unless-stopped
    command: mongod --auth --bind_ip_all
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache and Rate Limiting
  redis:
    image: redis:7-alpine
    container_name: hr-bot-redis
    ports:
      - "6379:6379"
    command: redis-server --requirepass redis_password --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - hr_bot_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Load Balancer and Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: hr-bot-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - hr-bot
    networks:
      - hr_bot_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: hr-bot-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    networks:
      - hr_bot_network
    restart: unless-stopped

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: hr-bot-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin_password
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - hr_bot_network
    restart: unless-stopped

  # ELK Stack for Log Management
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: hr-bot-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - hr_bot_network
    restart: unless-stopped

  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: hr-bot-logstash
    volumes:
      - ./elk/logstash.conf:/usr/share/logstash/pipeline/logstash.conf:ro
      - hr_bot_logs:/var/log/hr_bot:ro
    depends_on:
      - elasticsearch
    networks:
      - hr_bot_network
    restart: unless-stopped

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: hr-bot-kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - hr_bot_network
    restart: unless-stopped

  # Redis Insight for Redis Management
  redis-insight:
    image: redislabs/redisinsight:latest
    container_name: hr-bot-redis-insight
    ports:
      - "8001:8001"
    volumes:
      - redis_insight_data:/db
    networks:
      - hr_bot_network
    restart: unless-stopped

  # MongoDB Express for Database Management
  mongo-express:
    image: mongo-express:latest
    container_name: hr-bot-mongo-express
    ports:
      - "8081:8081"
    environment:
      - ME_CONFIG_MONGODB_ADMINUSERNAME=admin
      - ME_CONFIG_MONGODB_ADMINPASSWORD=admin_password
      - ME_CONFIG_MONGODB_SERVER=mongodb
      - ME_CONFIG_BASICAUTH_USERNAME=admin
      - ME_CONFIG_BASICAUTH_PASSWORD=admin_password
    depends_on:
      - mongodb
    networks:
      - hr_bot_network
    restart: unless-stopped

  # Health Check Service
  health-checker:
    image: alpine:latest
    container_name: hr-bot-health-checker
    command: >
      sh -c "
        apk add --no-cache curl &&
        while true; do
          echo 'Health check at $(date)' &&
          curl -f http://hr-bot:5011/health || echo 'Health check failed' &&
          sleep 60
        done
      "
    depends_on:
      - hr-bot
    networks:
      - hr_bot_network
    restart: unless-stopped

volumes:
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local
  redis_data:
    driver: local
  hr_bot_logs:
    driver: local
  hr_bot_data:
    driver: local
  nginx_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local
  redis_insight_data:
    driver: local

networks:
  hr_bot_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
