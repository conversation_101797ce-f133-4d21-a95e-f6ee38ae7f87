"""
Comprehensive security tests for Enterprise HR Bot.
Tests RBAC, input validation, rate limiting, and audit logging.
"""

import pytest
import time
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

# Import security components to test
from security.rbac_manager import RBACManager, Role, Permission
from security.input_validator import SecurityValidator, HRInputValidator, ValidationLevel
from security.rate_limiter import RedisRateLimiter, RateLimit, RateLimitAlgorithm, RateLimitType
from security.audit_logger import AuditLogger, AuditEvent, AuditEventType, AuditSeverity

class TestRBACManager:
    """Test Role-Based Access Control functionality"""
    
    @pytest.fixture
    def rbac_manager(self):
        """RBAC manager for testing"""
        return RBACManager()
    
    def test_role_hierarchy(self, rbac_manager):
        """Test role hierarchy and inheritance"""
        # Super Admin should have all permissions
        assert rbac_manager.has_permission("super_admin", Permission.VIEW_ALL_EMPLOYEES)
        assert rbac_manager.has_permission("super_admin", Permission.MODIFY_EMPLOYEE_DATA)
        assert rbac_manager.has_permission("super_admin", Permission.VIEW_PAYSLIP)
        
        # Employee should have basic permissions only
        assert rbac_manager.has_permission("employee", Permission.VIEW_OWN_DATA)
        assert rbac_manager.has_permission("employee", Permission.VIEW_HR_POLICIES)
        assert not rbac_manager.has_permission("employee", Permission.VIEW_ALL_EMPLOYEES)
        assert not rbac_manager.has_permission("employee", Permission.MODIFY_EMPLOYEE_DATA)
    
    def test_permission_assignment(self, rbac_manager):
        """Test permission assignment and checking"""
        # Test HR Manager permissions
        assert rbac_manager.has_permission("hr_manager", Permission.VIEW_ALL_EMPLOYEES)
        assert rbac_manager.has_permission("hr_manager", Permission.VIEW_PAYSLIP)
        assert rbac_manager.has_permission("hr_manager", Permission.GENERATE_REPORTS)
        
        # Test Team Lead permissions
        assert rbac_manager.has_permission("team_lead", Permission.VIEW_TEAM_DATA)
        assert not rbac_manager.has_permission("team_lead", Permission.VIEW_ALL_EMPLOYEES)
    
    def test_data_access_control(self, rbac_manager):
        """Test data access control based on roles"""
        # Test employee data access
        employee_data = {"emp_id": "12345", "name": "John Doe", "salary": 50000}
        
        # Employee can access own data
        assert rbac_manager.can_access_employee_data("12345", "12345", employee_data)
        
        # Employee cannot access other's data
        assert not rbac_manager.can_access_employee_data("12345", "67890", employee_data)
        
        # HR Manager can access any employee data
        rbac_manager.user_roles["hr_user"] = Role.HR_MANAGER
        assert rbac_manager.can_access_employee_data("hr_user", "12345", employee_data)
    
    def test_permission_decorator(self, rbac_manager):
        """Test permission decorator functionality"""
        from security.rbac_manager import require_permission
        
        @require_permission(Permission.VIEW_PAYSLIP)
        def get_payslip_data(emp_id):
            return f"Payslip for {emp_id}"
        
        # Mock current user context
        with patch('security.rbac_manager.get_current_user_id', return_value="hr_manager"):
            rbac_manager.user_roles["hr_manager"] = Role.HR_MANAGER
            result = get_payslip_data("12345")
            assert result == "Payslip for 12345"
        
        # Test permission denied
        with patch('security.rbac_manager.get_current_user_id', return_value="employee"):
            rbac_manager.user_roles["employee"] = Role.EMPLOYEE
            with pytest.raises(Exception):  # Should raise permission denied
                get_payslip_data("12345")

class TestInputValidator:
    """Test input validation and sanitization"""
    
    @pytest.fixture
    def security_validator(self):
        """Security validator for testing"""
        return SecurityValidator(ValidationLevel.STRICT)
    
    @pytest.fixture
    def hr_validator(self):
        """HR input validator for testing"""
        return HRInputValidator()
    
    def test_xss_detection(self, security_validator):
        """Test XSS attack detection"""
        # Test various XSS patterns
        xss_inputs = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src=x onerror=alert('xss')>",
            "<iframe src='javascript:alert(1)'></iframe>",
            "expression(alert('xss'))"
        ]
        
        for xss_input in xss_inputs:
            is_threat, threats = security_validator.detect_xss(xss_input)
            assert is_threat, f"Failed to detect XSS in: {xss_input}"
            assert len(threats) > 0
    
    def test_sql_injection_detection(self, security_validator):
        """Test SQL injection detection"""
        sql_inputs = [
            "'; DROP TABLE users; --",
            "1' OR '1'='1",
            "UNION SELECT * FROM passwords",
            "admin'--",
            "1; DELETE FROM employees"
        ]
        
        for sql_input in sql_inputs:
            is_threat, threats = security_validator.detect_sql_injection(sql_input)
            assert is_threat, f"Failed to detect SQL injection in: {sql_input}"
            assert len(threats) > 0
    
    def test_command_injection_detection(self, security_validator):
        """Test command injection detection"""
        cmd_inputs = [
            "; cat /etc/passwd",
            "| ls -la",
            "`whoami`",
            "$(id)",
            "&& rm -rf /",
            "../../../etc/passwd"
        ]
        
        for cmd_input in cmd_inputs:
            is_threat, threats = security_validator.detect_command_injection(cmd_input)
            assert is_threat, f"Failed to detect command injection in: {cmd_input}"
            assert len(threats) > 0
    
    def test_input_sanitization(self, security_validator):
        """Test input sanitization"""
        # Test HTML escaping
        html_input = "<b>Bold text</b> & special chars"
        sanitized = security_validator.sanitize_string(html_input, allow_html=False)
        assert "&lt;b&gt;" in sanitized
        assert "&amp;" in sanitized
        
        # Test HTML cleaning with allowed tags
        html_input = "<b>Bold</b><script>alert('xss')</script>"
        sanitized = security_validator.sanitize_string(html_input, allow_html=True)
        assert "<b>Bold</b>" in sanitized
        assert "<script>" not in sanitized
    
    def test_hr_specific_validation(self, hr_validator):
        """Test HR-specific input validation"""
        # Test valid HR request
        valid_data = {
            "emp_id": "12345",
            "question": "What is my leave balance?",
            "session_id": "550e8400-e29b-41d4-a716-446655440000",
            "email_type": "personal_email"
        }
        
        result = hr_validator.validate_hr_request(valid_data)
        assert result["valid"]
        assert len(result["errors"]) == 0
        assert len(result["security_threats"]) == 0
        
        # Test invalid HR request
        invalid_data = {
            "emp_id": "abc",  # Invalid format
            "question": "",   # Empty question
            "email_type": "invalid_type"  # Invalid email type
        }
        
        result = hr_validator.validate_hr_request(invalid_data)
        assert not result["valid"]
        assert len(result["errors"]) > 0
    
    def test_employee_id_validation(self, security_validator):
        """Test employee ID validation"""
        # Valid employee IDs
        valid_ids = ["1234", "12345", "1234567890"]
        for emp_id in valid_ids:
            valid, sanitized = security_validator.validate_employee_id(emp_id)
            assert valid
            assert sanitized == emp_id
        
        # Invalid employee IDs
        invalid_ids = ["", "123", "abc123", "12345678901"]
        for emp_id in invalid_ids:
            valid, error = security_validator.validate_employee_id(emp_id)
            assert not valid
            assert error is not None
    
    def test_comprehensive_validation(self, security_validator):
        """Test comprehensive validation pipeline"""
        # Clean input
        clean_input = "What is my leave balance?"
        result = security_validator.comprehensive_validate(clean_input)
        assert result["valid"]
        assert len(result["threats"]) == 0
        
        # Malicious input
        malicious_input = "<script>alert('xss')</script>What is my balance?"
        result = security_validator.comprehensive_validate(malicious_input)
        assert not result["valid"]
        assert len(result["threats"]) > 0

class TestRateLimiter:
    """Test rate limiting functionality"""
    
    @pytest.fixture
    def mock_redis(self):
        """Mock Redis client for rate limiter"""
        mock_client = Mock()
        mock_client.ping.return_value = True
        mock_client.pipeline.return_value = mock_client
        mock_client.execute.return_value = [None, 0, None, None]  # Mock pipeline results
        mock_client.zcard.return_value = 0
        mock_client.setex.return_value = True
        mock_client.get.return_value = None
        return mock_client
    
    @pytest.fixture
    def rate_limiter(self, mock_redis):
        """Rate limiter with mocked Redis"""
        with patch('security.rate_limiter.redis.Redis', return_value=mock_redis):
            return RedisRateLimiter()
    
    def test_rate_limit_configuration(self, rate_limiter):
        """Test rate limit configuration"""
        # Check that rate limits are properly configured
        assert "hr_query" in rate_limiter.rate_limits
        assert "auth" in rate_limiter.rate_limits
        assert "admin" in rate_limiter.rate_limits
        
        # Check specific limits
        hr_limit = rate_limiter.rate_limits["hr_query"][RateLimitType.PER_USER]
        assert hr_limit.requests == 100
        assert hr_limit.window == 3600
    
    def test_sliding_window_algorithm(self, rate_limiter, mock_redis):
        """Test sliding window rate limiting"""
        # Mock Redis responses for sliding window
        mock_redis.execute.return_value = [None, 5, None, None]  # 5 current requests
        
        result = rate_limiter._sliding_window_check(
            "test_key",
            RateLimit(requests=10, window=3600),
            int(time.time())
        )
        
        assert result.allowed
        assert result.remaining == 5  # 10 - 5 = 5 remaining
    
    def test_rate_limit_exceeded(self, rate_limiter, mock_redis):
        """Test rate limit exceeded scenario"""
        # Mock Redis to return limit exceeded
        mock_redis.execute.return_value = [None, 15, None, None]  # 15 requests (over limit of 10)
        
        result = rate_limiter._sliding_window_check(
            "test_key",
            RateLimit(requests=10, window=3600, block_duration=300),
            int(time.time())
        )
        
        assert not result.allowed
        assert result.remaining == 0
        assert result.retry_after == 300
    
    def test_multiple_rate_limit_types(self, rate_limiter, mock_redis):
        """Test multiple rate limit types"""
        # Mock successful rate limit checks
        mock_redis.execute.return_value = [None, 5, None, None]
        mock_redis.get.return_value = None  # Not blocked
        
        results = rate_limiter.check_rate_limit(
            endpoint="hr_query",
            user_id="12345",
            ip_address="***********"
        )
        
        # Should check both per-user and per-IP limits
        assert RateLimitType.PER_USER in results
        assert RateLimitType.PER_IP in results
    
    def test_rate_limit_headers(self, rate_limiter, mock_redis):
        """Test rate limit headers generation"""
        mock_redis.execute.return_value = [None, 5, None, None]
        mock_redis.get.return_value = None
        
        headers = rate_limiter.get_rate_limit_headers(
            endpoint="hr_query",
            user_id="12345",
            ip_address="***********"
        )
        
        assert "X-RateLimit-Remaining" in headers
        assert "X-RateLimit-Reset" in headers
        assert "X-RateLimit-Reset-After" in headers

class TestAuditLogger:
    """Test audit logging functionality"""
    
    @pytest.fixture
    def mock_mongo_client(self):
        """Mock MongoDB client for audit logger"""
        mock_client = Mock()
        mock_db = Mock()
        mock_collection = Mock()
        
        mock_client.__getitem__.return_value = mock_db
        mock_db.__getitem__.return_value = mock_collection
        mock_collection.insert_one.return_value = Mock()
        mock_collection.create_index.return_value = None
        mock_client.admin.command.return_value = {"ok": 1}
        
        return mock_client
    
    @pytest.fixture
    def audit_logger(self, mock_mongo_client):
        """Audit logger with mocked MongoDB"""
        with patch('security.audit_logger.MongoClient', return_value=mock_mongo_client):
            return AuditLogger()
    
    def test_audit_event_creation(self, audit_logger):
        """Test audit event creation and logging"""
        event_id = audit_logger.log_event(
            event_type=AuditEventType.LOGIN_SUCCESS,
            severity=AuditSeverity.MEDIUM,
            resource="login",
            action="authenticate",
            result="success",
            user_id="12345",
            ip_address="***********",
            details={"method": "OTP"}
        )
        
        assert event_id is not None
        assert len(event_id) == 16  # SHA256 hash truncated to 16 chars
    
    def test_pii_masking(self, audit_logger):
        """Test PII data masking"""
        details_with_pii = {
            "email": "<EMAIL>",
            "phone": "1234567890",
            "other_data": "safe_data"
        }
        
        masked_data = audit_logger._mask_pii(details_with_pii)
        
        assert masked_data["email"] != "<EMAIL>"
        assert "*" in masked_data["email"]
        assert masked_data["other_data"] == "safe_data"  # Non-PII unchanged
    
    def test_risk_score_calculation(self, audit_logger):
        """Test risk score calculation"""
        # High-risk event
        high_risk_event = AuditEvent(
            event_id="test",
            timestamp=datetime.now(),
            event_type=AuditEventType.SECURITY_VIOLATION,
            severity=AuditSeverity.CRITICAL,
            user_id="12345",
            session_id=None,
            ip_address="***********",
            user_agent=None,
            resource="test",
            action="test",
            result="failure",
            details={}
        )
        
        risk_score = audit_logger._calculate_risk_score(high_risk_event)
        assert risk_score >= 8  # Should be high risk
        
        # Low-risk event
        low_risk_event = AuditEvent(
            event_id="test",
            timestamp=datetime.now(),
            event_type=AuditEventType.DATA_VIEW,
            severity=AuditSeverity.LOW,
            user_id="12345",
            session_id=None,
            ip_address="***********",
            user_agent=None,
            resource="test",
            action="test",
            result="success",
            details={}
        )
        
        risk_score = audit_logger._calculate_risk_score(low_risk_event)
        assert risk_score <= 3  # Should be low risk
    
    def test_audit_decorator(self, audit_logger):
        """Test audit logging decorator"""
        from security.audit_logger import audit_log
        
        @audit_log(AuditEventType.DATA_VIEW, AuditSeverity.MEDIUM)
        def test_function(emp_id=None):
            return "success"
        
        # Mock the global audit logger
        with patch('security.audit_logger.audit_logger', audit_logger):
            result = test_function(emp_id="12345")
            assert result == "success"
    
    def test_search_functionality(self, audit_logger, mock_mongo_client):
        """Test audit log search functionality"""
        # Mock search results
        mock_collection = mock_mongo_client["jdsocial"]["audit_logs"]
        mock_cursor = Mock()
        mock_cursor.sort.return_value = mock_cursor
        mock_cursor.limit.return_value = [
            {
                "event_id": "test123",
                "event_type": "login_success",
                "user_id": "12345",
                "timestamp": datetime.now()
            }
        ]
        mock_collection.find.return_value = mock_cursor
        
        results = audit_logger.search_events(
            user_id="12345",
            event_type=AuditEventType.LOGIN_SUCCESS,
            limit=10
        )
        
        assert len(results) == 1
        assert results[0]["event_id"] == "test123"

class TestSecurityIntegration:
    """Integration tests for security components"""
    
    def test_end_to_end_security_pipeline(self):
        """Test complete security pipeline"""
        # This would test the full security pipeline
        # from input validation through RBAC to audit logging
        
        # Mock request data
        request_data = {
            "emp_id": "12345",
            "question": "What is my leave balance?",
            "session_id": "550e8400-e29b-41d4-a716-446655440000"
        }
        
        # 1. Input validation
        hr_validator = HRInputValidator()
        validation_result = hr_validator.validate_hr_request(request_data)
        assert validation_result["valid"]
        
        # 2. RBAC check
        rbac_manager = RBACManager()
        rbac_manager.user_roles["12345"] = Role.EMPLOYEE
        assert rbac_manager.has_permission("12345", Permission.VIEW_OWN_DATA)
        
        # 3. Rate limiting (mocked)
        with patch('security.rate_limiter.redis.Redis'):
            rate_limiter = RedisRateLimiter()
            allowed, _ = rate_limiter.is_request_allowed("hr_query", "12345", "***********")
            # Would be True in normal operation
        
        # 4. Audit logging (mocked)
        with patch('security.audit_logger.MongoClient'):
            audit_logger = AuditLogger()
            event_id = audit_logger.log_event(
                event_type=AuditEventType.DATA_VIEW,
                severity=AuditSeverity.MEDIUM,
                resource="hr_query",
                action="process",
                result="success",
                user_id="12345"
            )
            assert event_id is not None

if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v", "--tb=short"])
