"""
Comprehensive performance tests for Enterprise HR Bot.
Tests caching, connection pooling, async processing, and load handling.
"""

import pytest
import asyncio
import time
import concurrent.futures
from unittest.mock import Mock, patch, AsyncMock
import redis
import motor.motor_asyncio
from pymongo import MongoClient

# Import components to test
from performance.cache_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>, cache_result
from performance.connection_pool import Async<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SyncConnectionPool
from performance.async_optimizer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CircuitBreaker, AsyncToolExecutor

class TestCacheManager:
    """Test cache manager functionality"""
    
    @pytest.fixture
    def mock_redis(self):
        """Mock Redis client"""
        mock_client = Mock()
        mock_client.ping.return_value = True
        mock_client.get.return_value = None
        mock_client.set.return_value = True
        mock_client.delete.return_value = 1
        mock_client.exists.return_value = 0
        return mock_client
    
    @pytest.fixture
    def cache_manager(self, mock_redis):
        """Cache manager with mocked Redis"""
        with patch('performance.cache_manager.redis.Redis', return_value=mock_redis):
            return CacheManager()
    
    def test_cache_initialization(self, cache_manager):
        """Test cache manager initialization"""
        assert cache_manager is not None
        assert cache_manager.redis_client is not None
    
    def test_cache_set_get(self, cache_manager, mock_redis):
        """Test basic cache operations"""
        # Test set
        result = cache_manager.set("test_key", "test_value", ttl=300)
        assert result is True
        mock_redis.set.assert_called_once()
        
        # Test get
        mock_redis.get.return_value = '"test_value"'  # JSON serialized
        value = cache_manager.get("test_key")
        assert value == "test_value"
    
    def test_cache_delete(self, cache_manager, mock_redis):
        """Test cache deletion"""
        result = cache_manager.delete("test_key")
        assert result is True
        mock_redis.delete.assert_called_once_with("test_key")
    
    def test_cache_health_check(self, cache_manager, mock_redis):
        """Test cache health check"""
        health = cache_manager.health_check()
        assert health["status"] == "healthy"
        assert "redis_info" in health
        mock_redis.ping.assert_called_once()
    
    def test_cache_decorator(self, cache_manager, mock_redis):
        """Test cache decorator functionality"""
        call_count = 0
        
        @cache_result("test_func", ttl=300)
        def expensive_function(x, y):
            nonlocal call_count
            call_count += 1
            return x + y
        
        # First call - should execute function
        mock_redis.get.return_value = None
        result1 = expensive_function(1, 2)
        assert result1 == 3
        assert call_count == 1
        
        # Second call - should use cache
        mock_redis.get.return_value = '3'
        result2 = expensive_function(1, 2)
        assert result2 == 3
        assert call_count == 1  # Function not called again
    
    def test_cache_invalidation(self, cache_manager, mock_redis):
        """Test cache invalidation patterns"""
        # Test pattern-based invalidation
        mock_redis.keys.return_value = ["user:123:profile", "user:123:settings"]
        result = cache_manager.invalidate_pattern("user:123:*")
        assert result is True
        mock_redis.delete.assert_called()

class TestConnectionPool:
    """Test connection pool functionality"""
    
    @pytest.fixture
    def mock_motor_client(self):
        """Mock Motor async MongoDB client"""
        mock_client = AsyncMock()
        mock_db = AsyncMock()
        mock_client.__getitem__.return_value = mock_db
        return mock_client
    
    @pytest.fixture
    def mock_mongo_client(self):
        """Mock PyMongo sync client"""
        mock_client = Mock()
        mock_db = Mock()
        mock_client.__getitem__.return_value = mock_db
        return mock_client
    
    @pytest.fixture
    def async_pool(self, mock_motor_client):
        """Async connection pool with mocked clients"""
        with patch('performance.connection_pool.motor.motor_asyncio.AsyncIOMotorClient', return_value=mock_motor_client):
            return AsyncConnectionPool()
    
    @pytest.fixture
    def sync_pool(self, mock_mongo_client):
        """Sync connection pool with mocked clients"""
        with patch('performance.connection_pool.MongoClient', return_value=mock_mongo_client):
            return SyncConnectionPool()
    
    @pytest.mark.asyncio
    async def test_async_mongo_connection(self, async_pool, mock_motor_client):
        """Test async MongoDB connection"""
        async with async_pool.get_mongo_db() as db:
            assert db is not None
        
        # Verify client was accessed
        mock_motor_client.__getitem__.assert_called()
    
    def test_sync_mongo_connection(self, sync_pool, mock_mongo_client):
        """Test sync MongoDB connection"""
        with sync_pool.get_mongo_db() as db:
            assert db is not None
        
        # Verify client was accessed
        mock_mongo_client.__getitem__.assert_called()
    
    @pytest.mark.asyncio
    async def test_async_health_check(self, async_pool, mock_motor_client):
        """Test async pool health check"""
        mock_motor_client.admin.command = AsyncMock(return_value={"ok": 1})
        
        health = await async_pool.health_check()
        assert health["status"] == "healthy"
        assert "mongodb" in health
    
    def test_sync_health_check(self, sync_pool, mock_mongo_client):
        """Test sync pool health check"""
        mock_mongo_client.admin.command.return_value = {"ok": 1}
        
        health = sync_pool.health_check()
        assert health["status"] == "healthy"
        assert "mongodb" in health
    
    @pytest.mark.asyncio
    async def test_connection_pool_concurrency(self, async_pool):
        """Test connection pool under concurrent load"""
        async def get_connection():
            async with async_pool.get_mongo_db() as db:
                await asyncio.sleep(0.01)  # Simulate work
                return db is not None
        
        # Run 50 concurrent connections
        tasks = [get_connection() for _ in range(50)]
        results = await asyncio.gather(*tasks)
        
        # All connections should succeed
        assert all(results)

class TestAsyncOptimizer:
    """Test async optimization components"""
    
    @pytest.fixture
    def circuit_breaker(self):
        """Circuit breaker for testing"""
        return CircuitBreaker(
            failure_threshold=3,
            recovery_timeout=1,
            expected_exception=Exception
        )
    
    @pytest.fixture
    def async_request_manager(self):
        """Async request manager for testing"""
        return AsyncRequestManager(max_concurrent_requests=10)
    
    def test_circuit_breaker_closed_state(self, circuit_breaker):
        """Test circuit breaker in closed state"""
        assert circuit_breaker.state == "closed"
        
        # Successful call should keep circuit closed
        result = circuit_breaker.call(lambda: "success")
        assert result == "success"
        assert circuit_breaker.state == "closed"
    
    def test_circuit_breaker_open_state(self, circuit_breaker):
        """Test circuit breaker opening after failures"""
        # Cause failures to open circuit
        for _ in range(3):
            try:
                circuit_breaker.call(lambda: 1/0)  # Division by zero
            except:
                pass
        
        assert circuit_breaker.state == "open"
        
        # Should raise CircuitBreakerOpenException
        with pytest.raises(Exception):
            circuit_breaker.call(lambda: "test")
    
    def test_circuit_breaker_half_open_state(self, circuit_breaker):
        """Test circuit breaker half-open state"""
        # Open the circuit
        for _ in range(3):
            try:
                circuit_breaker.call(lambda: 1/0)
            except:
                pass
        
        # Wait for recovery timeout
        time.sleep(1.1)
        
        # Next call should put it in half-open state
        try:
            circuit_breaker.call(lambda: "success")
        except:
            pass
        
        # Should be back to closed after successful call
        assert circuit_breaker.state == "closed"
    
    @pytest.mark.asyncio
    async def test_async_request_manager_concurrency(self, async_request_manager):
        """Test async request manager concurrency control"""
        call_count = 0
        
        async def mock_request():
            nonlocal call_count
            call_count += 1
            await asyncio.sleep(0.1)
            return f"result_{call_count}"
        
        # Submit more requests than the limit
        tasks = []
        for i in range(15):
            task = async_request_manager.submit_request(f"req_{i}", mock_request())
            tasks.append(task)
        
        # Wait for all to complete
        results = await asyncio.gather(*tasks)
        
        # All should complete successfully
        assert len(results) == 15
        assert all("result_" in str(r) for r in results)
    
    @pytest.mark.asyncio
    async def test_async_tool_executor(self):
        """Test async tool executor"""
        executor = AsyncToolExecutor()
        
        # Mock tool functions
        async def mock_tool_1(arg):
            await asyncio.sleep(0.1)
            return f"tool1_result_{arg}"
        
        async def mock_tool_2(arg):
            await asyncio.sleep(0.1)
            return f"tool2_result_{arg}"
        
        tool_calls = [
            {"name": "tool1", "args": {"arg": "test1"}},
            {"name": "tool2", "args": {"arg": "test2"}}
        ]
        
        tool_functions = {
            "tool1": mock_tool_1,
            "tool2": mock_tool_2
        }
        
        results = await executor.execute_multiple_tools(tool_calls, tool_functions)
        
        assert len(results) == 2
        assert "tool1_result_test1" in str(results)
        assert "tool2_result_test2" in str(results)

class TestPerformanceIntegration:
    """Integration tests for performance components"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_performance(self):
        """Test end-to-end performance with all components"""
        # This would test the full pipeline with mocked dependencies
        
        # Mock the HR bot processing
        async def mock_hr_processing(question, emp_id):
            await asyncio.sleep(0.1)  # Simulate processing time
            return f"Answer for {question} from employee {emp_id}"
        
        # Test concurrent requests
        tasks = []
        for i in range(10):
            task = mock_hr_processing(f"question_{i}", f"emp_{i}")
            tasks.append(task)
        
        start_time = time.time()
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        # Should complete in reasonable time (less than 1 second for 10 concurrent requests)
        assert end_time - start_time < 1.0
        assert len(results) == 10
    
    def test_load_testing_simulation(self):
        """Simulate load testing scenario"""
        def simulate_request():
            # Simulate a request processing time
            time.sleep(0.05)  # 50ms processing time
            return "success"
        
        # Test with thread pool
        with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
            start_time = time.time()
            
            # Submit 100 requests
            futures = [executor.submit(simulate_request) for _ in range(100)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
            
            end_time = time.time()
        
        # Should handle 100 requests efficiently
        assert len(results) == 100
        assert all(r == "success" for r in results)
        
        # Should complete in reasonable time (less than 5 seconds)
        assert end_time - start_time < 5.0
        
        # Calculate throughput
        throughput = len(results) / (end_time - start_time)
        assert throughput > 20  # At least 20 requests per second

class TestMemoryAndResourceUsage:
    """Test memory usage and resource management"""
    
    def test_cache_memory_usage(self):
        """Test cache doesn't consume excessive memory"""
        cache_manager = CacheManager()
        
        # Store many items in cache (mocked)
        with patch.object(cache_manager, 'redis_client') as mock_redis:
            mock_redis.set.return_value = True
            
            # Store 1000 items
            for i in range(1000):
                cache_manager.set(f"key_{i}", f"value_{i}")
            
            # Should not raise memory errors
            assert mock_redis.set.call_count == 1000
    
    @pytest.mark.asyncio
    async def test_connection_pool_resource_cleanup(self):
        """Test connection pools properly clean up resources"""
        pool = AsyncConnectionPool()
        
        # Mock the cleanup
        with patch.object(pool, 'close') as mock_close:
            await pool.close()
            mock_close.assert_called_once()

if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v", "--tb=short"])
