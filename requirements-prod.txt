# Production-specific requirements for Enterprise HR Bot

# ASGI Server
uvicorn[standard]==0.24.0
gunicorn==21.2.0

# Performance and Monitoring
prometheus-client==0.19.0
psutil==5.9.6
memory-profiler==0.61.0

# Security
cryptography==41.0.7
bcrypt==4.1.2
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0

# Input Validation and Sanitization
bleach==6.1.0
validators==0.22.0
python-multipart==0.0.6

# Rate Limiting and Caching
redis[hiredis]==5.0.1
hiredis==2.2.3

# Database Drivers
motor==3.3.2
pymongo[srv]==4.6.0

# HTTP Client with connection pooling
aiohttp[speedups]==3.9.1
httpx[http2]==0.25.2

# Async utilities
asyncio-throttle==1.0.2
aiofiles==23.2.1

# Logging and Monitoring
structlog==23.2.0
python-json-logger==2.0.7
sentry-sdk[fastapi]==1.38.0

# Testing (for production testing)
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
httpx==0.25.2  # For testing HTTP endpoints

# Load Testing
locust==2.17.0

# Configuration Management
python-dotenv==1.0.0
pydantic-settings==2.1.0

# Health Checks
healthcheck==1.3.3

# Process Management
supervisor==4.2.5

# SSL/TLS
certifi==2023.11.17

# Data Validation
marshmallow==3.20.2
cerberus==1.3.5

# Utilities
click==8.1.7
rich==13.7.0
typer==0.9.0

# Production WSGI/ASGI
gevent==23.9.1
eventlet==0.33.3
